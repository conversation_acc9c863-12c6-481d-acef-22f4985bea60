// Import Firebase initialization logic that handles native vs web
import { initializeFirebaseNative, detectNativePlatform } from './src/utils/firebase-native.js';
import { openInAppBrowser } from './src/utils/inAppBrowser.js';

// Firebase services will be initialized dynamically
let auth, db, storage, analytics;
// AdMob (native apps only)
let AdMob = null;
const loadAdMob = () => {
  try {
    return import('@capacitor-community/admob')
      .then((mod) => mod.AdMob || null)
      .catch(() => null);
  } catch (e) {
    return Promise.resolve(null);
  }
};

// --- 1. CONFIGURATION & STATE ---
const appId = 'aura-global-app';

const initialAuthToken = typeof __initial_auth_token !== 'undefined' ? __initial_auth_token : null;

const GUIDE_USER_ID = 'the_official_guide_for_this_tour';

let userId, userLocation;
let isAppNativePlatform = false;
let isNativePlatform = false; // Global flag for Firebase platform detection
let watchPositionId, journeyId, unsubscribeFromMessages;
let isChatActive = false;
let currentActivityId = null;
let currentChatMode = 'group', currentViewMode = 'chat';
let emergencyContact = '', emergencyMessage = '', defaultSosAction = 'show-options';
let itinerary = [];
let authReadyPromise = null;
let mediaRecorder, audioChunks = [], isRecording = false;
// Promise-based approach to ensure Maps API is loaded before use.
const mapsApiReadyPromise = window.mapsApiReadyPromise || new Promise(resolve => {
    // If the promise isn't on the window, we set up a fallback.
    // The initMap function on window will be called regardless.
    window.initMap = () => {
        console.log("Google Maps API script loaded via app.js fallback.");
        resolve();
    };
});
let initTripPlannerMap;
let tripPlannerInitialCoords = null;
// --- 2. UI ELEMENT REFERENCES ---
const appContainer = document.getElementById('app-container');
const homeScreen = document.getElementById('home-screen');
const soloScreen = document.getElementById('solo-screen');
const groupScreen = document.getElementById('group-screen');
const infoScreen = document.getElementById('info-screen');
const userGuideScreen = document.getElementById('user-guide-screen');
const sponsorsScreen = document.getElementById('sponsors-screen');
const notesScreen = document.getElementById('notes-screen');
const itineraryList = document.getElementById('itinerary-list');
const activityInfoScreen = document.getElementById('activity-info-screen');
const chatContainer = document.getElementById('chat-container');
const codexScreen = document.getElementById('codex-screen');
const settingsModal = document.getElementById('settings-modal');
const sosOptionsModal = document.getElementById('sos-options-modal');
const actionConfirmModal = document.getElementById('action-confirm-modal');
const journeyModal = document.getElementById('journey-modal');
const photoGallery = document.getElementById('photo-gallery');
const galleryGrid = document.getElementById('gallery-grid');
const messageInputArea = document.getElementById('message-input-area');
const nameModal = document.getElementById('name-modal');
const nameInput = document.getElementById('name-input');
const saveNameButton = document.getElementById('save-name-button');
const pwaInstallModal = document.getElementById('pwa-install-modal');
const pwaInstallConfirmButton = document.getElementById('pwa-install-confirm-button');
const pwaInstallCancelButton = document.getElementById('pwa-install-cancel-button');
const pwaUpdateModal = document.getElementById('pwa-update-modal');
const pwaUpdateConfirmButton = document.getElementById('pwa-update-confirm-button');
const pwaUpdateCancelButton = document.getElementById('pwa-update-cancel-button');

const navHome = document.getElementById('nav-home');
const navSolo = document.getElementById('nav-solo');
const navGroup = document.getElementById('nav-group');
const navInfo = document.getElementById('nav-info');
const navCodex = document.getElementById('nav-codex');

const settingsButton = document.getElementById('settings-button');
const closeSettingsButton = document.getElementById('close-settings-button');
const settingsForm = document.getElementById('settings-form');
const emergencyContactInput = document.getElementById('emergency-contact');
const emergencyMessageInput = document.getElementById('emergency-message');

const homeSosButton = document.getElementById('home-sos-button');
const locationStatus = document.getElementById('location-status');
const cancelSos = document.getElementById('cancel-sos');
const alertContactButton = document.getElementById('alert-contact-button');
const actionConfirmTitle = document.getElementById('action-confirm-title');
const actionConfirmText = document.getElementById('action-confirm-text');
const closeActionConfirmModal = document.getElementById('close-action-confirm-modal');

const journeyStatusText = document.getElementById('journey-status-text');
const journeyLink = document.getElementById('journey-link');
const copyJourneyLinkButton = document.getElementById('copy-journey-link');
const stopJourneyButton = document.getElementById('stop-journey-button');
const soundScapeButton = document.getElementById('sound-scape-button');
const soundScapeStatus = document.getElementById('sound-scape-status');
const shareLocationButtonHome = document.getElementById('share-location-button-home');

// Helper function to set status text with proper white styling
function setSoundScapeStatus(text) {
    soundScapeStatus.textContent = text;
    if (text) {
        soundScapeStatus.style.color = '#ffffff';
        soundScapeStatus.style.fontWeight = 'bold';
        soundScapeStatus.style.textShadow = '2px 2px 4px rgba(0,0,0,0.8)';
    } else {
        soundScapeStatus.style.color = '';
        soundScapeStatus.style.fontWeight = '';
        soundScapeStatus.style.textShadow = '';
    }
}

const accessForm = document.getElementById('access-form');
const accessCodeInput = document.getElementById('access-code-input');
const errorMessage = document.getElementById('error-message');
const backToInfoBtn = document.getElementById('back-to-info');
const messageForm = document.getElementById('message-form');
const messageInput = document.getElementById('message-input');
const chatMessages = document.getElementById('chat-messages');
const loadingSpinner = document.getElementById('loading-spinner');
const chatModeSwitcher = document.getElementById('chat-mode-switcher');
const chatHeaderTitle = document.getElementById('chat-header-title');
const chatHeaderSubtitle = document.getElementById('chat-header-subtitle');
const modeGroupBtn = document.getElementById('mode-group');
const modePhotosBtn = document.getElementById('mode-photos');
const fileInput = document.getElementById('file-input');
const uploadStatus = document.getElementById('upload-status');
const regionSelect = document.getElementById('region-select');
const directoryList = document.getElementById('directory-list');
const directoryItemModal = document.getElementById('directory-item-modal');
const directoryItemContent = document.getElementById('directory-item-content');
const closeDirectoryModal = document.getElementById('close-directory-modal');

// --- 3. CORE FUNCTIONS ---

function showScreen(screenName) {
    homeScreen.classList.toggle('hidden', screenName !== 'home');
    soloScreen.classList.toggle('hidden', screenName !== 'solo');
    groupScreen.classList.toggle('hidden', screenName !== 'group');
    infoScreen.classList.toggle('hidden', screenName !== 'info');
    userGuideScreen.classList.toggle('hidden', screenName !== 'user-guide');
    sponsorsScreen.classList.toggle('hidden', screenName !== 'sponsors');
    notesScreen.classList.toggle('hidden', screenName !== 'notes');
    activityInfoScreen.classList.toggle('hidden', screenName !== 'activity-info');
    chatContainer.classList.toggle('hidden', screenName !== 'chat');
    codexScreen.classList.toggle('hidden', screenName !== 'codex');
    isChatActive = (screenName === 'chat');
    
    // Always re-render the itinerary when showing the group screen
    // to ensure the list is up-to-date after adding new activities.
    if (screenName === 'group') {
        renderItinerary();
    }

    navHome.classList.toggle('text-sky-600', screenName === 'home');
    navHome.classList.toggle('bg-sky-100', screenName === 'home');
    navHome.classList.toggle('text-slate-500', screenName !== 'home');
    navSolo.classList.toggle('text-sky-600', screenName === 'solo');
    navSolo.classList.toggle('bg-sky-100', screenName === 'solo');
    navSolo.classList.toggle('text-slate-500', screenName !== 'solo');
    navGroup.classList.toggle('text-sky-600', screenName === 'group' || screenName === 'activity-info' || screenName === 'chat');
    navGroup.classList.toggle('bg-sky-100', screenName === 'group' || screenName === 'activity-info' || screenName === 'chat');
    navGroup.classList.toggle('text-slate-500', screenName !== 'group' && screenName !== 'activity-info' && screenName !== 'chat');
    navInfo.classList.toggle('text-sky-600', ['info', 'user-guide', 'sponsors', 'notes'].includes(screenName));
    navInfo.classList.toggle('bg-sky-100', ['info', 'user-guide', 'sponsors', 'notes'].includes(screenName));
    navInfo.classList.toggle('text-slate-500', !['info', 'user-guide', 'sponsors', 'notes'].includes(screenName));

    navCodex.classList.toggle('text-sky-600', screenName === 'codex');
    navCodex.classList.toggle('bg-sky-100', screenName === 'codex');
    navCodex.classList.toggle('text-slate-500', screenName !== 'codex');
    navInfo.classList.toggle('text-sky-600', screenName === 'info' || screenName === 'user-guide' || screenName === 'sponsors' || screenName === 'notes');
    navInfo.classList.toggle('bg-sky-100', screenName === 'info' || screenName === 'user-guide' || screenName === 'sponsors' || screenName === 'notes');
    navInfo.classList.toggle('text-slate-500', !(screenName === 'info' || screenName === 'user-guide' || screenName === 'sponsors' || screenName === 'notes'));

    // AdMob banner toggle for native apps
    if (isNativePlatform && AdMob) {
        if (screenName === 'info') {
            // Show banner at bottom when Info screen is visible
            AdMob.showBanner({
                adId: 'ca-app-pub-4916662846779623/6691145167',
                adSize: 'ADAPTIVE_BANNER',
                position: 'BOTTOM_CENTER'
            }).catch(() => {});
        } else {
            AdMob.hideBanner().catch(() => {});
        }
    }
}

function getItinerary() { return JSON.parse(localStorage.getItem('auraItinerary') || '[]'); }
function saveItinerary(itinerary) { localStorage.setItem('auraItinerary', JSON.stringify(itinerary)); }

function deleteActivity(activityId) {
    if (confirm('Are you sure you want to delete this activity?')) {
        itinerary = itinerary.filter(activity => activity.id !== activityId);
        saveItinerary(itinerary);
        renderItinerary();
    }
}

function renderItinerary() {
    itineraryList.innerHTML = '';
    if (itinerary.length === 0) {
        itineraryList.innerHTML = `<p class="text-center text-slate-500">Your itinerary is empty.</p>`;
        return;
    }
    itinerary.forEach(activity => {
        const activityCard = document.createElement('div');
        activityCard.className = 'flex items-center p-4 bg-white border rounded-lg shadow-sm';

        // Conditionally create the image element or the placeholder
        let imageElement;
        if (activity.company_logo) {
            imageElement = `
                <img src="${activity.company_logo}" alt="${activity.name}" class="w-12 h-12 rounded-full object-cover mr-4">
            `;
        } else {
            imageElement = `
                <div class="w-12 h-12 bg-sky-500 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                    ${activity.name.charAt(0).toUpperCase()}
                </div>
            `;
        }

        activityCard.innerHTML = `
            <div class="flex-1 cursor-pointer" data-activity-id="${activity.id}">
                <div class="flex items-center">
                    ${imageElement}
                    <div class="flex-1">
                        <p class="font-bold text-slate-800">${activity.name}</p>
                        <p class="text-sm text-slate-600">${activity.date}</p>
                    </div>
                    <div class="text-slate-400">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m9 18 6-6-6-6"/>
                        </svg>
                    </div>
                </div>
            </div>
            <button class="delete-activity-button p-2 text-slate-400 hover:text-red-500" data-activity-id="${activity.id}">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="3 6 5 6 21 6"/><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/><line x1="10" y1="11" x2="10" y2="17"/><line x1="14" y1="11" x2="14" y2="17"/></svg>
            </button>
        `;
        
        const clickableArea = activityCard.querySelector('.flex-1.cursor-pointer');
        clickableArea.onclick = () => showActivityDetails(activity.id);

        const deleteButton = activityCard.querySelector('.delete-activity-button');
        deleteButton.onclick = () => deleteActivity(activity.id);

        itineraryList.appendChild(activityCard);
    });
}

function showActivityDetails(activityId) {
    console.log(`Attempting to show details for activityId: ${activityId}`);
    if (!activityId) {
        console.error("showActivityDetails called with null or undefined activityId.");
        return;
    }

    const activity = itinerary.find(a => a.id === activityId);
    
    if (!activity) {
        console.error(`Activity with ID '${activityId}' not found in the itinerary array.`, itinerary);
        alert(`Error: Could not find details for activity ${activityId}. Please try again.`);
        showScreen('group');
        return;
    }

    console.log("Found activity:", activity);
    currentActivityId = activityId;

    // Build company section
    let companySection = `
        <p class="text-md text-slate-500 mt-1">from ${activity.company}</p>
    `;
    if (activity.company_logo) {
        companySection = `
            <div class="flex items-center gap-4 mt-2">
                <img src="${activity.company_logo}" alt="${activity.company} Logo" class="w-12 h-12 rounded-full" onerror="this.style.display='none';">
                <div>
                    <p class="text-sm text-slate-500">from</p>
                    <p class="font-bold text-slate-700">${activity.company}</p>
                </div>
            </div>
        `;
    }

     // Build guide section only if guide data exists
     let guideSection = '';
     if (activity.guide && (activity.guide.name || activity.guide.title)) {
         guideSection = `
             <div class="mt-6">
                 <h3 class="text-lg font-semibold text-slate-700">Your Guide</h3>
                 <div class="flex items-center gap-4 mt-2 p-4 bg-slate-50 rounded-lg">
                     <img src="${activity.guide_photo || 'https://placehold.co/64x64/a3e635/ffffff?text=G'}" alt="Guide" class="w-16 h-16 rounded-full" onerror="this.onerror=null;this.src='https://placehold.co/64x64/a3e635/ffffff?text=G';">
                     <div>
                         <p class="font-bold text-slate-800">${activity.guide?.name || 'Guide'}</p>
                         <p class="text-sm text-slate-600">${activity.guide?.title || ''}</p>
                     </div>
                 </div>
             </div>
         `;
     }

     // Use WordPress featured image if available, fallback to featured_image field
     const activityImage = activity.wp_featured_image || activity.featured_image || '';

     activityInfoScreen.innerHTML = `
        <div class="p-3 border-b border-slate-200 bg-slate-50 text-center flex items-center">
            <button id="info-back-to-itinerary" class="p-2 rounded-full hover:bg-slate-200"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-slate-600"><path d="m15 18-6-6 6-6"/></svg></button>
            <div class="flex-1"><h1 class="text-xl font-bold text-slate-800">Activity Details</h1></div><div class="w-10"></div>
        </div>
        <div class="flex-1 p-6 overflow-y-auto">
            ${activityImage ? `<img src="${activityImage}" class="w-full h-40 object-cover rounded-lg mb-4" onerror="this.style.display='none'">` : ''}
            <h2 class="text-2xl font-bold text-slate-800">${activity.name}</h2>
            ${companySection}
            ${guideSection}
            <div class="mt-6"><h3 class="text-lg font-semibold text-slate-700">About this Activity</h3><p class="text-slate-600 mt-2 text-sm leading-relaxed">${activity.description}</p></div>
            <div class="mt-6"><h3 class="text-lg font-semibold text-slate-700">Documents</h3><div id="document-list" class="mt-2 space-y-2"></div></div>
        </div>
        <div class="p-4 border-t bg-white"><button id="go-to-chat-button" class="w-full py-3 bg-sky-500 text-white rounded-lg font-semibold hover:bg-sky-600 active:scale-95">Go to Activity Chat</button></div>
     `;
     
     const docList = activityInfoScreen.querySelector('#document-list');
     if(activity.documents && activity.documents.length > 0){
        activity.documents.forEach(doc => {
            const docEl = document.createElement('div');
            docEl.className = 'flex items-center gap-3 p-3 bg-slate-100 rounded-lg hover:bg-slate-200 cursor-pointer';
            docEl.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><line x1="16" y1="13" x2="8" y2="13"/></svg><span>${escapeHTML(doc.name)}</span>`;
            
            // Only make it clickable if there's a valid URL
            if (doc.url && doc.url !== '#') {
                docEl.addEventListener('click', (e) => {
                    e.preventDefault();
                    openInAppBrowser(doc.url);
                });
            } else {
                docEl.classList.remove('hover:bg-slate-200', 'cursor-pointer');
                docEl.classList.add('opacity-50', 'cursor-not-allowed');
            }
            
            docList.appendChild(docEl);
        });
     } else {
        docList.innerHTML = `<p class="text-sm text-slate-500">No documents provided for this activity.</p>`
     }

     document.getElementById(`info-back-to-itinerary`).addEventListener('click', () => showScreen('group'));
     document.getElementById(`go-to-chat-button`).addEventListener('click', () => {
        chatHeaderTitle.textContent = 'Activity Chat';
        chatHeaderSubtitle.textContent = activity.name;
        showScreen('chat');
        if (auth) setupChat();
     });
document.getElementById('chat-back-to-activity-info').addEventListener('click', () => showScreen('activity-info'));
     showScreen('activity-info');
}

function loadSettings() {
    emergencyContact = localStorage.getItem('auraEmergencyContact') || '';
    emergencyMessage = localStorage.getItem('auraEmergencyMessage') || 'I am in an emergency and need help. This is my location.';
    defaultSosAction = localStorage.getItem('auraDefaultSosAction') || 'show-options';

    // Log settings loading (non-sensitive data only)
    console.log('Loading settings:', {
        hasEmergencyContact: !!emergencyContact,
        hasEmergencyMessage: !!emergencyMessage,
        defaultSosAction
    });

    // Ensure form elements exist before setting values
    if (emergencyContactInput) {
        emergencyContactInput.value = emergencyContact;
    }
    if (emergencyMessageInput) {
        emergencyMessageInput.value = emergencyMessage;
    }
    
    // Set radio button with error handling
    const radioButton = document.querySelector(`input[name="sos-action"][value="${defaultSosAction}"]`);
    if (radioButton) {
        radioButton.checked = true;
    } else {
        console.warn('Radio button not found for:', defaultSosAction);
        // Fallback to default
        const defaultRadio = document.querySelector(`input[name="sos-action"][value="show-options"]`);
        if (defaultRadio) defaultRadio.checked = true;
    }
}

function handleSaveSettings(e) {
    e.preventDefault();

    // Get and validate values from form
    const newEmergencyContact = emergencyContactInput.value.trim();
    const newEmergencyMessage = emergencyMessageInput.value.trim();
    const newDefaultSosAction = document.querySelector('input[name="sos-action"]:checked')?.value || 'show-options';

    // Validate emergency contact
    if (newEmergencyContact && !validatePhoneNumber(newEmergencyContact)) {
        alert('Please enter a valid phone number for emergency contact (e.g., +1234567890 or 1234567890)');
        return;
    }

    // Validate emergency message
    const messageValidation = validateMessageInput(newEmergencyMessage);
    if (!messageValidation.isValid) {
        alert(`Emergency message error: ${messageValidation.error}`);
        return;
    }

    // Validate SOS action
    const validSosActions = ['show-options', 'alert-contact'];
    if (!validSosActions.includes(newDefaultSosAction)) {
        alert('Invalid SOS action selected');
        return;
    }

    // Sanitize inputs
    emergencyContact = sanitizeInput(newEmergencyContact);
    emergencyMessage = sanitizeMessageInput(newEmergencyMessage) || 'I am in an emergency and need help. This is my location.';
    defaultSosAction = newDefaultSosAction;

    // Save to localStorage
    localStorage.setItem('auraEmergencyContact', emergencyContact);
    localStorage.setItem('auraEmergencyMessage', emergencyMessage);
    localStorage.setItem('auraDefaultSosAction', defaultSosAction);

    // Log settings save (non-sensitive data only)
    console.log('Settings saved:', {
        hasEmergencyContact: !!emergencyContact,
        hasEmergencyMessage: !!emergencyMessage,
        defaultSosAction
    });

    // Show confirmation
    const saveButton = settingsForm.querySelector('button[type="submit"]');
    const originalText = saveButton.textContent;
    saveButton.textContent = 'Settings Saved!';
    saveButton.style.backgroundColor = '#10b981';

    setTimeout(() => {
        saveButton.textContent = originalText;
        saveButton.style.backgroundColor = '';
        settingsModal.classList.add('hidden');
    }, 1500);
}

function handleSos() {
    const action = isChatActive ? 'show-options' : defaultSosAction;
    if (action === 'alert-contact') {
        triggerDefaultSosAction();
    } else {
        showSosOptions();
    }
}

async function showSosOptions() {
    sosOptionsModal.classList.remove('hidden');

    // Show location consent for emergency use
    const consentGiven = await showLocationConsentDialog(true); // true indicates emergency use
    if (!consentGiven) {
        locationStatus.textContent = 'Location access denied. Emergency features limited.';
        return;
    }

    locationStatus.textContent = 'Getting your location...';
    navigator.geolocation.getCurrentPosition(position => {
        userLocation = { latitude: position.coords.latitude, longitude: position.coords.longitude };
        locationStatus.textContent = 'Location acquired!';
    }, () => {
        userLocation = null;
        locationStatus.textContent = 'Could not get location. Please ensure you have a network connection or GPS signal.';
    });
}

async function triggerDefaultSosAction() {
    if (!emergencyContact) {
        actionConfirmTitle.textContent = "Action Failed";
        actionConfirmText.innerHTML = `<strong>Please set an emergency contact in Settings first.</strong>`;
        actionConfirmModal.classList.remove('hidden');
        return;
    }

    actionConfirmTitle.textContent = "SOS Action Triggered";
    actionConfirmText.textContent = 'Getting location and preparing alert...';
    actionConfirmModal.classList.remove('hidden');
    
    await handleSmsAlert(true); // Call the new async function
}

async function handleSmsAlert(isDefaultAction = false) {
    if (!emergencyContact) {
        alert('Please set an emergency contact in Settings first.');
        return;
    }

    // For non-default actions, show consent dialog
    if (!isDefaultAction) {
        const consentGiven = await showLocationConsentDialog(true);
        if (!consentGiven) {
            sosOptionsModal.classList.add('hidden');
            return;
        }
    }

    const statusElement = isDefaultAction ? actionConfirmText : locationStatus;
    if (statusElement === soundScapeStatus) {
        setSoundScapeStatus('Getting your location...');
    } else {
        statusElement.textContent = 'Getting your location...';
    }

    try {
        const position = await new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, { enableHighAccuracy: true });
        });
        const userLocation = { latitude: position.coords.latitude, longitude: position.coords.longitude };

        // Sanitize emergency message before sending
        const sanitizedMessage = sanitizeMessageInput(emergencyMessage);
        let fullMessage = sanitizedMessage;
        fullMessage += `\nMy location: https://www.google.com/maps?q=${userLocation.latitude},${userLocation.longitude} `;

        const smsUrl = `sms:${emergencyContact}?body=${encodeURIComponent(fullMessage)}`;
        window.location.href = smsUrl;

        if (isDefaultAction) {
            actionConfirmText.innerHTML = `<strong>Opening messaging app to alert ${escapeHTML(emergencyContact)}...</strong>`;
        } else {
            sosOptionsModal.classList.add('hidden');
        }

    } catch (error) {
        console.error("Failed to get location for SMS:", error);
        if (statusElement === soundScapeStatus) {
            setSoundScapeStatus('Could not get location. Sending alert without it.');
        } else {
            statusElement.textContent = 'Could not get location. Sending alert without it.';
        }
        // Still try to send message without location
        let fullMessage = `${emergencyMessage} (Location not available)`; // Added space
        const smsUrl = `sms:${emergencyContact}?body=${encodeURIComponent(fullMessage)}`;
        window.location.href = smsUrl;
    }
}

async function stopJourney() {
    if (watchPositionId) navigator.geolocation.clearWatch(watchPositionId);
    watchPositionId = null;
    if (journeyId && db) {
        const journeyDocRef = window.fb.doc(db, `/artifacts/${appId}/public/data/journeys/${journeyId}`);
        await window.fb.updateDoc(journeyDocRef, { active: false });
    }
    journeyModal.classList.add('hidden');
    journeyId = null;
}

function copyJourneyLink() {
    if (copyJourneyLinkButton.disabled) return;
    const textArea = document.createElement("textarea");
    textArea.value = journeyLink.textContent;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    alert('Link copied!');
}

async function handleAccessCode(e) {
    e.preventDefault();
    const enteredCode = accessCodeInput.value.trim().toUpperCase();
    if (!enteredCode) return;
    
    errorMessage.textContent = 'Searching...';

    try {
        await authReadyPromise;
        const activityPath = `artifacts/${appId}/public/data/activities`;
        const activityRef = window.fb.doc(db, activityPath, enteredCode);
        // Add timeout for iOS Simulator Firestore issues
        const getDocPromise = window.fb.getDoc(activityRef);
        const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('FIRESTORE_TIMEOUT')), 8000)
        );
        const activitySnap = await Promise.race([getDocPromise, timeoutPromise]);

        if (activitySnap.exists()) {
            const activityData = activitySnap.data();
            // IMPORTANT: Ensure the activity has an 'id' field. Fallback to the code if it doesn't.
            if (!activityData.id) {
                activityData.id = enteredCode;
            }
            activityData.code = enteredCode;
            
            console.log('Activity found from Firebase:', activityData);
            
            const activityExists = itinerary.some(a => a.id === activityData.id);
            if (!activityExists) {
                itinerary.push(activityData);
                saveItinerary(itinerary);
                renderItinerary();
                // Save user name for chat
                nameModal.classList.remove('hidden');
                saveNameButton.onclick = () => {
                    const userName = nameInput.value.trim();
                    if (userName) {
                        localStorage.setItem('auraUserName', userName);
                        nameModal.classList.add('hidden');
                        showActivityDetails(activityData.id);
                    }
                };
            }
            
            accessCodeInput.value = '';
            errorMessage.textContent = '';
            
            // This must be called after the state is updated.
            showActivityDetails(activityData.id);

        } else {
            console.log('Activity not found for code:', enteredCode);
            errorMessage.textContent = 'Invalid Activity Code.';
        }
    } catch (error) {
        // Security: Log detailed error for developers, show generic message to users
        logSecureError('Activity lookup failed', error, { activityCode: enteredCode });
        errorMessage.textContent = 'Unable to find activity. Please check your code and try again.';
    }
}

async function authenticateUser() {
    authReadyPromise = new Promise(async (resolve) => {
        try {
            // Initialize Firebase (native on iOS/Android, web on PWA)
            console.log('🔥 Initializing Firebase...');
            const firebaseServices = await initializeFirebaseNative();
            auth = firebaseServices.auth;
            db = firebaseServices.db;
            isNativePlatform = firebaseServices.isNativePlatform;

            // Import Firebase functions based on platform
            let signInAnonymously, signInWithCustomToken, doc, setDoc, updateDoc, collection, addDoc, onSnapshot, query, serverTimestamp, getDoc, where, getDocs, arrayUnion, ref, uploadBytes, getDownloadURL, logEvent, onAuthStateChanged;

            if (firebaseServices.isNativePlatform) {
                console.log('🔥 Using native Firebase on iOS');
                // Native Firebase functions are provided by firebaseServices
                signInAnonymously = firebaseServices.signInAnonymously;
                signInWithCustomToken = firebaseServices.signInWithCustomToken;
                onAuthStateChanged = firebaseServices.onAuthStateChanged;
                doc = firebaseServices.doc;
                setDoc = firebaseServices.setDoc;
                updateDoc = firebaseServices.updateDoc;
                collection = firebaseServices.collection;
                addDoc = firebaseServices.addDoc;
                onSnapshot = firebaseServices.onSnapshot;
                query = firebaseServices.query;
                serverTimestamp = firebaseServices.serverTimestamp;
                getDoc = firebaseServices.getDoc;
                where = firebaseServices.where;
                getDocs = firebaseServices.getDocs;
                arrayUnion = firebaseServices.arrayUnion;
                ref = firebaseServices.ref;
                uploadBytes = firebaseServices.uploadBytes;
                getDownloadURL = firebaseServices.getDownloadURL;
                logEvent = firebaseServices.logEvent;
                storage = firebaseServices.storage;
                analytics = firebaseServices.analytics;
            } else {
                console.log('🔥 Using web Firebase SDK');
                // Import web SDK functions
                const authModule = await import("firebase/auth");
                const firestoreModule = await import("firebase/firestore");
                const storageModule = await import("firebase/storage");
                const analyticsModule = await import("firebase/analytics");

                signInAnonymously = authModule.signInAnonymously;
                signInWithCustomToken = authModule.signInWithCustomToken;
                onAuthStateChanged = authModule.onAuthStateChanged;
                doc = firestoreModule.doc;
                setDoc = firestoreModule.setDoc;
                updateDoc = firestoreModule.updateDoc;
                collection = firestoreModule.collection;
                addDoc = firestoreModule.addDoc;
                onSnapshot = firestoreModule.onSnapshot;
                query = firestoreModule.query;
                serverTimestamp = firestoreModule.serverTimestamp;
                getDoc = firestoreModule.getDoc;
                where = firestoreModule.where;
                getDocs = firestoreModule.getDocs;
                arrayUnion = firestoreModule.arrayUnion;
                ref = storageModule.ref;
                uploadBytes = storageModule.uploadBytes;
                getDownloadURL = storageModule.getDownloadURL;
                logEvent = analyticsModule.logEvent;

                // Initialize storage and analytics for web
                const { auth: webAuth, db: webDb, storage: webStorage, analytics: webAnalytics } = await import('./src/firebase-init.js');
                storage = webStorage;
                analytics = webAnalytics;
            }

            // Store Firebase functions globally for other parts of the app
            window.firebaseFunctions = {
                signInAnonymously, signInWithCustomToken, onAuthStateChanged, doc, setDoc, updateDoc, collection, addDoc, onSnapshot, query, serverTimestamp, getDoc, where, getDocs, arrayUnion, ref, uploadBytes, getDownloadURL, logEvent
            };

            // Create helper functions for easier access
            window.fb = {
                doc: (...args) => window.firebaseFunctions.doc(...args),
                setDoc: (...args) => window.firebaseFunctions.setDoc(...args),
                updateDoc: (...args) => window.firebaseFunctions.updateDoc(...args),
                collection: (...args) => window.firebaseFunctions.collection(...args),
                addDoc: (...args) => window.firebaseFunctions.addDoc(...args),
                onSnapshot: (...args) => window.firebaseFunctions.onSnapshot(...args),
                query: (...args) => window.firebaseFunctions.query(...args),
                serverTimestamp: (...args) => window.firebaseFunctions.serverTimestamp(...args),
                getDoc: (...args) => window.firebaseFunctions.getDoc(...args),
                where: (...args) => window.firebaseFunctions.where(...args),
                getDocs: (...args) => window.firebaseFunctions.getDocs(...args),
                arrayUnion: (...args) => window.firebaseFunctions.arrayUnion(...args),
                ref: (...args) => window.firebaseFunctions.ref(...args),
                uploadBytes: (...args) => window.firebaseFunctions.uploadBytes(...args),
                getDownloadURL: (...args) => window.firebaseFunctions.getDownloadURL(...args),
                logEvent: (...args) => window.firebaseFunctions.logEvent(...args)
            };

            // Expose Firebase services globally for the Codex feature
            window.auraFirebase = {
                db,
                auth,
                collection,
                getDocs,
                doc,
                getDoc,
                updateDoc,
                arrayUnion,
                setDoc,
                serverTimestamp,
                onAuthStateChanged: onAuthStateChanged || null
            };

            const currentUser = auth.currentUser;
            if (currentUser) {
                userId = currentUser.uid;
                console.log('✅ User already authenticated:', userId);

                // Dispatch firebase-ready event for Codex feature
                window.dispatchEvent(new CustomEvent('firebase-ready'));
                console.log("Dispatched firebase-ready event from app.js");

                resolve();
            } else {
                console.log('🔐 Signing in...');
                if (initialAuthToken) {
                    const uc = await (signInAnonymously ? signInWithCustomToken(auth, initialAuthToken) : auth.signInWithCustomToken(initialAuthToken));
                    userId = uc.user ? uc.user.uid : uc.uid;
                } else {
                    const uc = await (signInAnonymously ? signInAnonymously(auth) : auth.signInAnonymously());
                    userId = uc.user ? uc.user.uid : uc.uid;
                }
                console.log('✅ User authenticated:', userId);

                // Dispatch firebase-ready event for Codex feature
                window.dispatchEvent(new CustomEvent('firebase-ready'));
                console.log("Dispatched firebase-ready event from app.js");

                resolve();
            }
        } catch (error) {
            console.error("Auth Error:", error);
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                userId = 'localhost-dev-user-' + Date.now();
                console.log('🔧 Using mock user ID for localhost development:', userId);
            }

            // Still dispatch firebase-ready event even on error to prevent hanging
            window.dispatchEvent(new CustomEvent('firebase-ready'));
            console.log("Dispatched firebase-ready event from app.js (error case)");

            resolve(); // Don't block app
        }
    });
    return authReadyPromise;
}



function generatePrivateChatId(uid1, uid2) { return [uid1, uid2].sort().join('_'); }

async function setupChat() {
    if (!db || !userId) {
        console.log('Chat setup failed: missing db or userId', { db: !!db, hasUserId: !!userId });
        return;
    }
    if (unsubscribeFromMessages) unsubscribeFromMessages();
    
    // Initialize UI state
    updateUIForChatView();
    
    chatMessages.innerHTML = '';
    loadingSpinner.style.display = 'flex';

    // Use original Firebase path structure
    let messagesCollectionPath = `artifacts/${appId}/public/data/aura-chat-rooms/${currentActivityId}/messages`;

    console.log('Setting up chat with path:', messagesCollectionPath);
    console.log('Activity ID:', currentActivityId);

    console.log('🔥 Creating Firestore collection reference...');
    const messagesCollection = window.fb.collection(db, messagesCollectionPath);
    console.log('✅ Collection reference created');

    console.log('🔥 Creating Firestore query...');
    const q = window.fb.query(messagesCollection);
    console.log('✅ Query created');

    console.log('🔥 Setting up onSnapshot listener... (this may hang on iOS Simulator)');

    // Add timeout for iOS Simulator onSnapshot hanging
    let snapshotTimeout = setTimeout(() => {
        console.error('⏰ onSnapshot timeout after 10 seconds - likely iOS Simulator networking issue');
        loadingSpinner.style.display = 'none';
        chatMessages.innerHTML = `<div class="text-center p-4">
            <p class="text-yellow-600 mb-2">⚠️ Chat loading timed out</p>
            <p class="text-sm text-gray-500">This is a known iOS Simulator limitation. The app works normally on real devices.</p>
            <button onclick="setupChat()" class="mt-2 px-4 py-2 bg-blue-500 text-white rounded">Retry</button>
        </div>`;
    }, 10000);

    unsubscribeFromMessages = window.fb.onSnapshot(q, (querySnapshot) => {
        console.log('✅ onSnapshot callback fired! Received data.');
        console.log('📊 QuerySnapshot size:', querySnapshot.size);
        clearTimeout(snapshotTimeout); // Cancel timeout since we got data
        loadingSpinner.style.display = 'none';
        const messages = [];
        querySnapshot.forEach((doc) => {
            const data = doc.data();
            // Log message received (non-sensitive data only)
            console.log('Message received:', {
                id: doc.id,
                type: data.type || 'text',
                hasText: !!data.text,
                hasFile: !!data.fileURL,
                timestamp: data.timestamp
            });
            messages.push({ id: doc.id, ...data });
        });
        messages.sort((a, b) => (a.timestamp?.seconds || 0) - (b.timestamp?.seconds || 0));
        // Security: Only log message count, not content
        console.log('Received messages:', messages.length);
        renderMessages(messages);
    }, (error) => {
        console.log('❌ onSnapshot error callback fired');
        clearTimeout(snapshotTimeout); // Cancel timeout since we got an error
        // Security: Log detailed error for developers, show generic message to users
        logSecureError('Message loading failed', error, { path: messagesCollectionPath });
        loadingSpinner.style.display = 'none';
        chatMessages.innerHTML = `<p class="text-red-500 text-center">Unable to load messages. Please check your connection and try again.</p>`;
    });
}

// File upload security validation
function validateFileUpload(file) {
    // Define allowed file types and their MIME types
    const allowedTypes = {
        // Images
        'image/jpeg': { maxSize: 3 * 1024 * 1024, extensions: ['.jpg', '.jpeg'] }, // 3MB
        'image/png': { maxSize: 3 * 1024 * 1024, extensions: ['.png'] },
        'image/gif': { maxSize: 2 * 1024 * 1024, extensions: ['.gif'] }, // 2MB
        'image/webp': { maxSize: 3 * 1024 * 1024, extensions: ['.webp'] },

        // Documents
        'application/pdf': { maxSize: 5 * 1024 * 1024, extensions: ['.pdf'] }, // 5MB
        'text/plain': { maxSize: 1 * 1024 * 1024, extensions: ['.txt'] }, // 1MB

        // Audio
        'audio/mpeg': { maxSize: 5 * 1024 * 1024, extensions: ['.mp3'] }, // 5MB
        'audio/wav': { maxSize: 5 * 1024 * 1024, extensions: ['.wav'] },
        'audio/ogg': { maxSize: 5 * 1024 * 1024, extensions: ['.ogg'] }
    };

    // Check file type
    if (!allowedTypes[file.type]) {
        return { isValid: false, error: 'File type not allowed. Please upload images, PDFs, text files, or audio files only.' };
    }

    // Check file size
    const typeConfig = allowedTypes[file.type];
    if (file.size > typeConfig.maxSize) {
        const maxSizeMB = Math.round(typeConfig.maxSize / (1024 * 1024));
        return { isValid: false, error: `File too large. Maximum size for ${file.type} is ${maxSizeMB}MB.` };
    }

    // Check file extension matches MIME type
    const fileName = file.name.toLowerCase();
    const hasValidExtension = typeConfig.extensions.some(ext => fileName.endsWith(ext));
    if (!hasValidExtension) {
        return { isValid: false, error: 'File extension does not match file type.' };
    }

    // Check for suspicious file names
    const suspiciousPatterns = [
        /\.exe$/i, /\.bat$/i, /\.cmd$/i, /\.scr$/i, /\.pif$/i,
        /\.com$/i, /\.jar$/i, /\.js$/i, /\.vbs$/i, /\.php$/i,
        /\.asp$/i, /\.jsp$/i, /\.html$/i, /\.htm$/i
    ];

    if (suspiciousPatterns.some(pattern => pattern.test(fileName))) {
        return { isValid: false, error: 'File type not allowed for security reasons.' };
    }

    return { isValid: true };
}

function sanitizeFileName(fileName) {
    // Remove or replace dangerous characters
    return fileName
        .replace(/[<>:"/\\|?*]/g, '_') // Replace dangerous characters
        .replace(/\.\./g, '_') // Remove directory traversal attempts
        .replace(/^\.+/, '') // Remove leading dots
        .substring(0, 100) // Limit length
        .trim();
}

async function compressImage(file, options = {}) {
    return new Promise((resolve, reject) => {
        const { quality = 0.7, maxWidth = 1920, maxHeight = 1080 } = options;
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (event) => {
            const img = new Image();
            img.src = event.target.result;
            img.onload = () => {
                const canvas = document.createElement('canvas');
                let { width, height } = img;

                if (width > height) {
                    if (width > maxWidth) {
                        height *= maxWidth / width;
                        width = maxWidth;
                    }
                } else {
                    if (height > maxHeight) {
                        width *= maxHeight / height;
                        height = maxHeight;
                    }
                }

                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0, width, height);

                canvas.toBlob((blob) => {
                    if (blob) {
                        resolve(blob);
                    } else {
                        reject(new Error('Canvas to Blob conversion failed'));
                    }
                }, 'image/jpeg', quality);
            };
            img.onerror = (error) => reject(error);
        };
        reader.onerror = (error) => reject(error);
    });
}

async function handleFileUpload(event) {
    const file = event.target.files[0];
    if (!file || !userId) {
      console.error('Cannot upload file: missing file or userId', { file: !!file, userId });
      uploadStatus.textContent = 'Upload failed: missing user authentication. Please try again.';
      setTimeout(() => uploadStatus.classList.add('hidden'), 3000);
      return;
    }

    // Comprehensive file validation
    const validationResult = validateFileUpload(file);
    if (!validationResult.isValid) {
        uploadStatus.textContent = `Upload failed: ${validationResult.error}`;
        uploadStatus.classList.remove('hidden');
        setTimeout(() => uploadStatus.classList.add('hidden'), 5000);
        return;
    }

    uploadStatus.textContent = `Uploading ${file.name}...`;
    uploadStatus.classList.remove('hidden');

    try {
        let fileToUpload = file;
        const originalFileName = sanitizeFileName(file.name);
        let finalStorageFileName = `${userId}-${Date.now()}-${originalFileName}`;

        if (file.type.startsWith('image/')) {
            const compressedBlob = await compressImage(file);
            fileToUpload = compressedBlob;
            // Use secure filename for compressed images
            finalStorageFileName = `${userId}-${Date.now()}-compressed.jpg`;
        }

        // Update rate limiting tracking before file upload
        const userUploadRateLimitRef = window.fb.doc(db, `users/${userId}/rateLimiting/lastUploadTime`);
        await window.fb.setDoc(userUploadRateLimitRef, { timestamp: window.fb.serverTimestamp() });

        const storageRef = window.fb.ref(storage, `chat_uploads/${currentActivityId}/${finalStorageFileName}`);
        const snapshot = await window.fb.uploadBytes(storageRef, fileToUpload);
        const downloadURL = await window.fb.getDownloadURL(snapshot.ref);

        let messageData = {
            senderId: userId,
            senderName: localStorage.getItem('auraUserName') || 'Anonymous',
            timestamp: window.fb.serverTimestamp(),
            fileName: originalFileName, // Keep original filename for display
            fileURL: downloadURL,
            type: file.type.startsWith('image/') ? 'image' : 'file'
        };

        await sendStructuredMessage(messageData);
        uploadStatus.classList.add('hidden');
    } catch(error) {
        console.error("Upload failed:", error);
        uploadStatus.textContent = 'Upload failed. Please try again.';
        setTimeout(() => uploadStatus.classList.add('hidden'), 3000);
    }
}

async function sendStructuredMessage(data) {
    if (!userId || !db) {
        console.error('Cannot send message: missing userId or db', { userId, db: !!db });
        throw new Error('Cannot send message: missing userId or db');
    }

    // Check if user is authenticated
    if (!auth.currentUser) {
        console.error('User not authenticated for messaging');
        throw new Error('User not authenticated for messaging');
    }

    // Use the same path structure as setupChat
    let messagesCollectionPath = `artifacts/${appId}/public/data/aura-chat-rooms/${currentActivityId}/messages`;

    // Log message sending (non-sensitive data only)
    console.log('Sending message to path:', messagesCollectionPath, {
        type: data.type || 'text',
        hasText: !!data.text,
        hasFile: !!data.fileURL
    });

    try {
        // Send the message first - rate limiting is handled by Firestore rules
        const docRef = await window.fb.addDoc(window.fb.collection(db, messagesCollectionPath), data);
        console.log('Message sent successfully with ID:', docRef.id);

        // Update rate limiting tracking after successful message send
        try {
            const userRateLimitRef = window.fb.doc(db, `users/${userId}/rateLimiting/lastMessageTime`);
            await window.fb.setDoc(userRateLimitRef, { timestamp: window.fb.serverTimestamp() });
        } catch (rateLimitError) {
            // Rate limit tracking failure shouldn't prevent message sending
            console.warn('Rate limit tracking failed:', rateLimitError);
        }

        return docRef;
    } catch (error) {
        // Security: Log detailed error for developers, show generic message to users
        logSecureError('Message sending failed', error, { path: messagesCollectionPath });
        throw error; // Re-throw so calling function can handle it
    }
}

// Store messages locally to prevent disappearing
let localMessages = [];

function renderMessages(messages) {
    // Update local messages store
    localMessages = [...messages];
    
    chatMessages.innerHTML = '';
    galleryGrid.innerHTML = '';
    
    const images = messages.filter(m => m.type === 'image');
    if (images.length === 0) {
        galleryGrid.innerHTML = `<p class="col-span-3 text-center text-slate-500 text-sm p-4">No photos have been shared yet.</p>`;
    } else {
        images.forEach(msg => {
            const imgElement = document.createElement('div');
            imgElement.className = 'aspect-square bg-cover bg-center rounded-md cursor-pointer';
            imgElement.style.backgroundImage = `url(${msg.fileURL})`;
            imgElement.onclick = () => window.open(msg.fileURL, '_blank');
            galleryGrid.appendChild(imgElement);
        });
    }

    if (currentViewMode === 'chat') {
         if (messages.length === 0) {
            const welcomeText = currentChatMode === 'group' ? 'Welcome to the group chat!' : 'This is a private chat with your guide.';
            chatMessages.innerHTML = `<p class="text-center text-slate-500 text-sm p-4">${welcomeText}</p>`;
        } else {
            // Log message rendering
            console.log('Rendering', messages.length, 'messages');
            messages.forEach((msg, index) => {
                let messageElement;
                const isCurrentUser = msg.senderId === userId;
                const alignmentClass = isCurrentUser ? 'items-end' : 'items-start';
                const bubbleColor = isCurrentUser ? 'bg-sky-500 text-white' : 'bg-slate-200 text-slate-800';
                const senderName = isCurrentUser ? 'You' : msg.senderName || 'User';

                if (msg.type !== 'image') { // Only render non-image messages in the chat view
                    if (msg.type === 'SOS') {
                        messageElement = document.createElement('div');
                        messageElement.className = 'my-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-800 rounded-r-lg w-full';

                        // Use secure SOS rendering
                        if (window.XSSProtection) {
                            messageElement.innerHTML = window.XSSProtection.renderSecureSOS({
                                senderRole: msg.senderRole,
                                location: msg.location
                            });
                        } else {
                            // Fallback with basic escaping
                            messageElement.innerHTML = `<p class="font-bold text-lg">🆘 SOS ALERT 🆘</p><p class="text-sm">Sent by ${escapeHTML(msg.senderRole)}.</p><p class="text-sm mt-1">Location: <a href="https://www.google.com/maps?q=${msg.location.latitude},${msg.location.longitude}" target="_blank" rel="noopener noreferrer" class="underline hover:text-red-600">View on Map</a></p>`;
                        }
                    } else {
                        messageElement = document.createElement('div');
                        messageElement.className = `flex flex-col mb-4 ${alignmentClass}`;
                        messageElement.setAttribute('data-message-id', msg.id || index);

                        // Use secure message rendering
                        let safeSenderName = escapeHTML(senderName);
                        let contentHtml;

                        if (window.XSSProtection) {
                            const secureMessage = window.XSSProtection.renderSecureMessage({
                                text: msg.text,
                                senderName: msg.senderName,
                                fileName: msg.fileName,
                                fileURL: msg.fileURL,
                                type: msg.type,
                                isCurrentUser: isCurrentUser
                            });
                            safeSenderName = secureMessage.senderName;
                            contentHtml = secureMessage.contentHtml;
                        } else {
                            // Fallback with basic escaping
                            if (msg.type === 'file') {
                                contentHtml = `<a href="${msg.fileURL}" target="_blank" rel="noopener noreferrer" class="flex items-center gap-2 p-3 rounded-lg ${isCurrentUser ? 'bg-sky-600' : 'bg-slate-300'}"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><line x1="10" y1="9" x2="8" y2="9"/></svg><span>${escapeHTML(msg.fileName)}</span></a>`;
                            } else {
                                contentHtml = `<p class="text-sm">${escapeHTML(msg.text || 'No message content')}</p>`;
                            }
                        }

                        messageElement.innerHTML = `<div class="text-xs text-slate-500 mb-1 px-1">${safeSenderName}</div><div class="p-3 rounded-2xl message-bubble ${bubbleColor}">${contentHtml}</div>`;
                    }
                    chatMessages.appendChild(messageElement);
                }
            });
        }
        // Scroll to bottom after a short delay to ensure rendering is complete
        setTimeout(() => {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }, 100);
    }
}

async function handleMessageSubmit(e) {
    e.preventDefault();
    const messageText = messageInput.value.trim();
    if (!messageText) return;

    // Validate and sanitize message input
    const validationResult = validateMessageInput(messageText);
    if (!validationResult.isValid) {
        alert(`Message not sent: ${validationResult.error}`);
        return;
    }

    const sanitizedMessage = sanitizeMessageInput(messageText);

    const tempId = `temp_${Date.now()}`;
    const optimisticMessage = {
        id: tempId,
        text: sanitizedMessage,
        senderId: userId,
        timestamp: { seconds: Date.now() / 1000 },
        isOptimistic: true
    };

    localMessages.push(optimisticMessage);
    renderMessages(localMessages);
    messageInput.value = '';

    try {
        await sendStructuredMessage({
            text: sanitizedMessage,
            senderId: userId,
            senderName: sanitizeInput(localStorage.getItem('auraUserName') || 'Anonymous'),
            timestamp: window.fb.serverTimestamp()
        });
        console.log('Message sent successfully');
    } catch (error) {
        // Security: Log detailed error for developers, show generic message to users
        logSecureError('Message submission failed', error);
        // Handle error: remove optimistic message or show error state
        localMessages = localMessages.filter(m => m.id !== tempId);
        renderMessages(localMessages);
        // Show user-friendly error message
        showUserError('Unable to send message. Please check your connection and try again.');
    }
}

function escapeHTML(str) {
    // Use enhanced XSS protection if available, fallback to basic escaping
    if (window.XSSProtection) {
        return window.XSSProtection.escapeHTML(str);
    }

    // Fallback basic escaping
    if (!str) return '';
    const p = document.createElement('p');
    p.appendChild(document.createTextNode(str));
    return p.innerHTML;
}

// Location Privacy Consent Dialog
function showLocationConsentDialog(isEmergency = false) {
    return new Promise((resolve) => {
        // Check if user has already given consent recently
        const consentKey = isEmergency ? 'locationConsentEmergency' : 'locationConsentGeneral';
        const lastConsent = localStorage.getItem(consentKey);
        const consentExpiry = 24 * 60 * 60 * 1000; // 24 hours

        if (lastConsent && (Date.now() - parseInt(lastConsent)) < consentExpiry) {
            resolve(true);
            return;
        }

        // Create consent dialog
        const dialog = document.createElement('div');
        dialog.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        dialog.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-md mx-4">
                <h3 class="text-lg font-bold mb-4">
                    ${isEmergency ? '🚨 Emergency Location Access' : '📍 Location Sharing Permission'}
                </h3>
                <div class="mb-4 text-sm text-gray-700">
                    ${isEmergency ?
                        '<p><strong>Emergency Use:</strong> This app needs access to your location to help emergency contacts find you in case of an emergency.</p>' :
                        '<p><strong>Location Sharing:</strong> This app wants to access your location to share your real-time position with others.</p>'
                    }
                    <br>
                    <p><strong>Privacy Notice:</strong></p>
                    <ul class="list-disc list-inside space-y-1">
                        <li>Your location will only be shared when you explicitly choose to do so</li>
                        <li>Location data is transmitted securely and not stored permanently</li>
                        <li>You can stop sharing your location at any time</li>
                        <li>No location data is shared with third parties</li>
                    </ul>
                </div>
                <div class="flex gap-3">
                    <button id="consent-deny" class="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                        Deny
                    </button>
                    <button id="consent-allow" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        Allow
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // Handle user choice
        dialog.querySelector('#consent-allow').onclick = () => {
            localStorage.setItem(consentKey, Date.now().toString());
            document.body.removeChild(dialog);
            resolve(true);
        };

        dialog.querySelector('#consent-deny').onclick = () => {
            document.body.removeChild(dialog);
            resolve(false);
        };

        // Close on background click
        dialog.onclick = (e) => {
            if (e.target === dialog) {
                document.body.removeChild(dialog);
                resolve(false);
            }
        };
    });
}

function updateUIForChatView() {
    const isPhotos = currentViewMode === 'photos';
    
    modeGroupBtn.classList.toggle('bg-white', currentChatMode === 'group' && !isPhotos);
    modePhotosBtn.classList.toggle('bg-white', isPhotos);
    
    modeGroupBtn.classList.toggle('shadow', currentChatMode === 'group' && !isPhotos);
    modePhotosBtn.classList.toggle('shadow', isPhotos);

    chatMessages.classList.toggle('hidden', isPhotos);
    photoGallery.classList.toggle('hidden', !isPhotos);
    
    // Toggle visibility of input areas
    const photoUploadArea = document.getElementById('photo-upload-area');
    messageInputArea.classList.toggle('hidden', isPhotos);
    photoUploadArea.classList.toggle('hidden', !isPhotos);
    photoUploadArea.classList.toggle('flex', isPhotos);


}

// Check if running in Capacitor environment
function isCapacitorApp() {
    return window.Capacitor && window.Capacitor.isNativePlatform();
}

// Global flag to ensure voice recorder listener is only set up once
let voiceRecorderListenerSetup = false;

// Handle recording result (can be called from event or directly)
async function handleRecordingResult(result) {
    try {
        const { Filesystem } = window.Capacitor.Plugins;
        const { Share } = window.Capacitor.Plugins;

        // Debug: Check what data we received
        console.log('📊 Recording result structure:', Object.keys(result));
        console.log('📊 Recording value:', result.value);

        // Handle different possible data structures
        let recordingData;
        if (result.value && result.value.recordDataBase64) {
            recordingData = result.value.recordDataBase64;
        } else if (result.value && result.value.mimeType && result.value.msDuration) {
            // Alternative structure - try different property names
            recordingData = result.value.recordDataBase64 || result.value.data || result.value.base64String;
        } else if (result.recordDataBase64) {
            recordingData = result.recordDataBase64;
        } else {
            throw new Error('No recording data found in result');
        }

        if (!recordingData) {
            throw new Error('Recording data is empty or undefined');
        }

        console.log('📏 Recording data length:', recordingData.length);

        const fileName = `SoundScape-${new Date().toISOString().replace(/[:.]/g, '-')}.wav`;

        // Request filesystem permissions first
        console.log('🔐 Checking filesystem permissions...');
        try {
            const permissions = await Filesystem.checkPermissions();
            console.log('📋 Current filesystem permissions:', permissions);

            if (permissions.publicStorage !== 'granted') {
                console.log('🔐 Requesting filesystem permissions...');
                const permissionResult = await Filesystem.requestPermissions();
                console.log('📋 Filesystem permission result:', permissionResult);
            }
        } catch (permError) {
            console.log('⚠️ Filesystem permission check failed:', permError);
        }

        // Save directly to Downloads folder (publicly accessible)
        console.log('💾 Attempting to save recording to Downloads...');

        try {
            // Save to Downloads directory (publicly accessible)
            await Filesystem.writeFile({
                path: fileName,
                data: recordingData,
                directory: 'DOCUMENTS'  // Use DOCUMENTS which maps to Downloads on Android
            });

            console.log('✅ File saved to Downloads folder:', fileName);
            setSoundScapeStatus('✅ Recording saved to Downloads folder!');

        } catch (saveError) {
            console.error('❌ Save to Downloads failed:', saveError);
            setSoundScapeStatus('❌ Error saving recording. Please try again.');
        }

        setTimeout(() => {
            setSoundScapeStatus('');
        }, 8000); // Longer display time

    } catch (error) {
        console.error("❌ Error saving recording:", error);
        console.error("❌ Error details:", error.message, error.stack);
        setSoundScapeStatus(`Error saving recording: ${error.message}`);
        setTimeout(() => { setSoundScapeStatus(''); }, 6000);
    }
}

// Set up voice recorder event listener globally (called once during app init)
async function setupVoiceRecorderListener() {
    if (voiceRecorderListenerSetup || !isCapacitorApp()) {
        return;
    }

    try {
        const { VoiceRecorder } = window.Capacitor.Plugins;
        console.log('🎤 Setting up global voice recorder listener...');

        VoiceRecorder.addListener('recordingFinished', async (result) => {
            console.log('🎉 Recording finished event received:', result);
            await handleRecordingResult(result);
        });

        voiceRecorderListenerSetup = true;
        console.log('✅ Voice recorder listener set up successfully');

    } catch (error) {
        console.error('❌ Failed to set up voice recorder listener:', error);
    }
}

async function handleSoundScape() {
    setSoundScapeStatus('');

    if (isRecording) {
        if (isCapacitorApp()) {
            // Stop Capacitor voice recording
            try {
                const { VoiceRecorder } = window.Capacitor.Plugins;
                console.log('🛑 Stopping recording...');
                const result = await VoiceRecorder.stopRecording();
                isRecording = false;
                soundScapeButton.innerHTML = 'Sound<br>Scape';
                setSoundScapeStatus('Recording stopped. Saving...');
                console.log('✅ Stop recording command sent');
                console.log('📊 Stop recording result:', result);

                // Process the recording data directly since event might not fire
                if (result && result.value && result.value.recordDataBase64) {
                    console.log('🎉 Got recording data directly from stopRecording');
                    await handleRecordingResult(result);
                } else {
                    console.log('⏳ No direct data, waiting for recordingFinished event...');
                }
            } catch (error) {
                console.error("❌ Error stopping Capacitor recording:", error);
                setSoundScapeStatus('Error stopping recording.');
                isRecording = false;
            }
        } else {
            // Stop web recording
            mediaRecorder.stop();
            isRecording = false;
            soundScapeButton.innerHTML = 'Sound<br>Scape';
            setSoundScapeStatus('Recording stopped. Saving...');
        }
        return;
    }

    try {
        if (isCapacitorApp()) {
            // Ensure event listener is set up before starting recording
            await setupVoiceRecorderListener();

            // Use Capacitor voice recorder for native apps
            const { VoiceRecorder } = window.Capacitor.Plugins;

            // Request permissions first
            console.log('🔐 Checking microphone permissions...');
            const hasPermission = await VoiceRecorder.hasAudioRecordingPermission();
            if (!hasPermission.value) {
                console.log('🔐 Requesting microphone permission...');
                const permissionResult = await VoiceRecorder.requestAudioRecordingPermission();
                if (!permissionResult.value) {
                    setSoundScapeStatus('❌ Microphone permission denied.');
                    return;
                }
            }
            console.log('✅ Microphone permission granted');

            // Start recording
            console.log('🎤 Starting recording...');
            await VoiceRecorder.startRecording();
            isRecording = true;
            soundScapeButton.innerHTML = 'Stop<br>Recording';
            setSoundScapeStatus('Recording...');
            console.log('✅ Recording started successfully');

        } else {
            // Use web API for PWA/browser
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            isRecording = true;
            audioChunks = [];
            mediaRecorder = new MediaRecorder(stream);

            mediaRecorder.ondataavailable = event => {
                audioChunks.push(event.data);
            };

            mediaRecorder.onstop = () => {
                const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                const audioUrl = URL.createObjectURL(audioBlob);
                const a = document.createElement('a');
                a.href = audioUrl;
                a.download = `SoundScape-${new Date().toISOString()}.wav`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(audioUrl);
                setSoundScapeStatus('Recording saved to downloads.');
                // Get all audio tracks and stop them to turn off the mic indicator
                stream.getTracks().forEach(track => track.stop());
                setTimeout(() => { setSoundScapeStatus(''); }, 4000);
            };

            mediaRecorder.start();
            soundScapeButton.innerHTML = 'Stop<br>Recording';
            setSoundScapeStatus('Recording...');
        }

    } catch (error) {
        console.error("Error accessing microphone:", error);
        if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
            setSoundScapeStatus('Microphone permission denied.');
        } else {
            setSoundScapeStatus('Could not start recording.');
        }
        isRecording = false;
    }
}

// --- 4. EVENT LISTENERS & INITIALIZATION ---

function showUpdateNotification(worker) {
    if (pwaUpdateModal && pwaUpdateConfirmButton && pwaUpdateCancelButton) {
        pwaUpdateModal.classList.remove('hidden');
        pwaUpdateConfirmButton.onclick = () => {
            pwaUpdateModal.classList.add('hidden');
            if (worker) {
                // Tell the new service worker to take over...
                worker.postMessage({ type: 'SKIP_WAITING' });
                // ...and then immediately reload the page to apply the update.
                window.location.reload();
            }
        };
        pwaUpdateCancelButton.onclick = () => {
            pwaUpdateModal.classList.add('hidden');
        };
    } else {
        // Fallback for when the modal isn't in the DOM
        if (confirm('New version available! Refresh to update?')) {
            worker.postMessage({ type: 'SKIP_WAITING' });
        }
    }
}

/**
 * Tracks how the app is launched and logs the event to the appropriate analytics service
 * (Capacitor plugin for native, Web SDK for browsers).
 */
function trackDisplayMode() {
    let displayMode = 'browser';
    const isPWA = window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone || document.referrer.includes('android-app://');

    if (isPWA) {
        displayMode = 'standalone';
    } else if (window.matchMedia('(display-mode: minimal-ui)').matches) {
        displayMode = 'minimal-ui';
    } else if (window.matchMedia('(display-mode: fullscreen)').matches) {
        displayMode = 'fullscreen';
    }

    const eventName = 'app_launched';
    const eventParams = { display_mode: displayMode };

    // Use Firebase for analytics
    if (analytics && window.firebaseFunctions?.logEvent) {
        console.log(`Analytics: Logging event '${eventName}'`, eventParams);
        try {
            window.firebaseFunctions.logEvent(analytics, eventName, eventParams);
        } catch (error) {
            console.error("Firebase Analytics logging failed.", error);
        }
    } else {
        // This case might happen if the function is called before initialization is complete
        console.log(`Analytics: App launched in '${displayMode}' mode. (Analytics service not yet available).`);
    }
}

document.addEventListener('DOMContentLoaded', () => {
    itinerary = getItinerary();

    loadSettings();
    authenticateUser();
    renderItinerary();
    trackDisplayMode();

    // Initialize AdMob when running natively
    (async () => {
        try {
            // Detect native environment via Capacitor
            isAppNativePlatform = !!window.Capacitor && (typeof window.Capacitor.isNativePlatform === 'function'
                ? window.Capacitor.isNativePlatform()
                : (window.Capacitor.getPlatform && window.Capacitor.getPlatform() !== 'web'));
        } catch (_) {
            isAppNativePlatform = false;
        }
        if (isAppNativePlatform) {
            AdMob = await loadAdMob();
            if (AdMob) {
                try { 
                    // Android doesn't support promise-based initialize
                    if (window.Capacitor.getPlatform() === 'android') {
                        AdMob.initialize({ requestTrackingAuthorization: true });
                    } else {
                        await AdMob.initialize({ requestTrackingAuthorization: true });
                    }
                } catch (_) {}
            }
        }
    })();

    // Set up voice recorder listener for Capacitor apps
    if (isCapacitorApp()) {
        setupVoiceRecorderListener();
    }
    
    // Register Service Worker for PWA functionality
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('SW registered: ', registration);
                    // Check for updates
                    registration.onupdatefound = () => {
                        const installingWorker = registration.installing;
                        if (installingWorker) {
                            installingWorker.onstatechange = () => {
                                if (installingWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // New content is available, prompt user to refresh
                                    showUpdateNotification(installingWorker);
                                }
                            };
                        }
                    };
                }).catch(registrationError => {
                    console.log('SW registration failed: ', registrationError);
                });

            // This event fires when the service worker controlling the page changes.
            // We reload the page to ensure the new service worker's assets are used.
            // The 'controllerchange' event listener for reloading the page has been removed.
            // The reload is now handled directly by the update confirmation button's
            // onclick handler to provide a more reliable, single-refresh update process.
            let refreshing;
            navigator.serviceWorker.addEventListener('controllerchange', () => {
                if (refreshing) return;
                // The reload is now handled by the button's onclick handler.
                // window.location.reload();
                refreshing = true;
            });
        });
    }
    
    // Handle install prompt for PWA
    let deferredPrompt;
    const installPwaButton = document.getElementById('install-pwa-button');

    // Check PWA installability
    function checkPWAInstallability() {
        // Check if already installed
        if (window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone === true) {
            console.log('PWA: App is already installed');
            return;
        }

        // Log basic PWA status
        console.log('PWA: Checking installability requirements...');

        // Show install button if no beforeinstallprompt after delay
        setTimeout(() => {
            if (!deferredPrompt) {
                console.log('PWA: Install prompt not available (normal for some browsers like Firefox)');
            }
        }, 3000);
    }

    window.addEventListener('beforeinstallprompt', (e) => {
        console.log('PWA: beforeinstallprompt event fired');
        // Prevent the mini-infobar from appearing on mobile
        e.preventDefault();
        // Stash the event so it can be triggered later.
        deferredPrompt = e;

        // Don't show the prompt if the app is already installed
        if (window.matchMedia('(display-mode: standalone)').matches ||
            window.navigator.standalone === true) {
            console.log('PWA: App already installed, not showing prompt');
            return;
        }

        // Show the install prompt modal after a short delay to ensure DOM is ready
        setTimeout(() => {
            if (pwaInstallModal) {
                console.log('PWA: Showing install modal');
                pwaInstallModal.classList.remove('hidden');
            }
            // Update the UI to show the install button in the info screen as a fallback
            if (installPwaButton) {
                console.log('PWA: Showing install button');
                installPwaButton.classList.remove('hidden');
            }
        }, 1000); // 1 second delay to ensure user sees the app first
    });

    // Run installability check after page load
    window.addEventListener('load', () => {
        setTimeout(checkPWAInstallability, 2000);
    });

    if (installPwaButton) {
        installPwaButton.addEventListener('click', async () => {
            console.log('PWA: Install button clicked');
            if (!deferredPrompt) {
                console.log('PWA: No deferred prompt available - app may not be installable or already installed');
                return;
            }

            try {
                // Hide the app provided install button
                installPwaButton.classList.add('hidden');
                // Show the install prompt
                await deferredPrompt.prompt();
                // Wait for the user to respond to the prompt
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`PWA: User response to the install prompt: ${outcome}`);
                // We've used the prompt, and can't use it again, throw it away
                deferredPrompt = null;
            } catch (error) {
                console.error('PWA: Error during installation:', error);
                // Show the button again if there was an error
                installPwaButton.classList.remove('hidden');
            }
        });
    }

    if (pwaInstallConfirmButton) {
        pwaInstallConfirmButton.addEventListener('click', async () => {
            console.log('PWA: Install confirm button clicked');
            if (!deferredPrompt) {
                console.log('PWA: No deferred prompt available');
                pwaInstallModal.classList.add('hidden');
                return;
            }

            try {
                await deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`PWA: User response to the install prompt: ${outcome}`);
                deferredPrompt = null;
                pwaInstallModal.classList.add('hidden');
            } catch (error) {
                console.error('PWA: Error during installation:', error);
                pwaInstallModal.classList.add('hidden');
            }
        });
    }

    if (pwaInstallCancelButton) {
        pwaInstallCancelButton.addEventListener('click', () => {
            console.log('PWA: Install cancelled by user');
            pwaInstallModal.classList.add('hidden');
        });
    }

    // Handle successful PWA installation
    window.addEventListener('appinstalled', (evt) => {
        console.log('PWA: App was installed successfully');
        // Hide the app-provided install button
        if (installPwaButton) {
            installPwaButton.classList.add('hidden');
        }
        if (pwaInstallModal) {
            pwaInstallModal.classList.add('hidden');
        }
        deferredPrompt = null;
    });

    // --- SOLO TRIP PLANNER LOGIC ---
    const soloScreenContainer = document.getElementById('solo-screen');
    if (soloScreenContainer) {
        const saveItineraryBtn = document.getElementById('saveItineraryBtn');
        const exportItineraryBtn = document.getElementById('exportItineraryBtn');
        const savedItinerariesList = document.getElementById('saved-itineraries-list');
        const addStopBtn = document.getElementById('addStopBtn');
        const calculateTravelBtn = document.getElementById('calculateTravelBtn');
        const analyzeBtn = document.getElementById('analyzeBtn');
        const stopsTableBody = document.querySelector('#stopsTable tbody');
        const travelSummary = document.getElementById('travel-summary');
        let stops = [];
        let savedItineraries = [];
        let map, geocoder, directionsRenderer;
        let tripMarkers = []; // Array to store all markers for the trip planner

        // New global function to be called from other parts of the app, like Codex
        window.navigateToMap = (lat, lng) => {
            tripPlannerInitialCoords = { lat, lng };
            // Switch to the trip planner screen and initialize the map
            showScreen('solo');
            initTripPlannerMap();
        };

        initTripPlannerMap = async () => {
            try {
                // Check if Google Maps is already loaded or being loaded
                if (!window.google || !window.google.maps) {
                    // Check if script is already in DOM
                    const existingScript = document.querySelector('script[src*="maps.googleapis.com"]');
                    
                    if (!existingScript) {
                        await new Promise((resolve, reject) => {
                            try {
                                // Use security manager to get validated API key
                                const mapsApiKey = window.MapsSecurityManager?.getSecureApiKey() ||
                                                 window.GOOGLE_MAPS_CONFIG?.apiKey ||
                                                 'your-google-maps-api-key-here';

                                if (mapsApiKey === 'your-google-maps-api-key-here') {
                                    throw new Error('Google Maps API key is missing or using placeholder value. Please check your environment variables.');
                                }

                                const script = document.createElement('script');
                                script.src = `https://maps.googleapis.com/maps/api/js?key=${mapsApiKey}&libraries=marker,geometry&loading=async`;
                                script.async = true;
                                script.defer = true;
                                script.onload = () => {
                                    // Log successful API load
                                    window.MapsSecurityManager?.logSecurityEvent('Google Maps API loaded successfully');
                                    resolve();
                                };
                                script.onerror = (error) => {
                                    // Log API load failure
                                    window.MapsSecurityManager?.logSecurityEvent('Google Maps API failed to load', { error: error.message });
                                    reject(error);
                                };
                                document.head.appendChild(script);
                            } catch (securityError) {
                                console.error('🚨 Maps Security Error:', securityError.message);
                                window.MapsSecurityManager?.logSecurityEvent('Maps API security validation failed', { error: securityError.message });
                                reject(securityError);
                            }
                        });
                    } else {
                        // Wait for existing script to load
                        await new Promise(resolve => {
                            if (window.google && window.google.maps) {
                                resolve();
                            } else {
                                existingScript.addEventListener('load', resolve);
                            }
                        });
                    }
                }

                // Wait for Google Maps to be fully loaded
                await new Promise(resolve => {
                    const checkGoogleMaps = () => {
                        if (window.google && window.google.maps && window.google.maps.Geocoder) {
                            resolve();
                        } else {
                            setTimeout(checkGoogleMaps, 100);
                        }
                    };
                    checkGoogleMaps();
                });

                const initialCenter = tripPlannerInitialCoords
                    ? { lat: tripPlannerInitialCoords.lat, lng: tripPlannerInitialCoords.lng }
                    : { lat: -34.9285, lng: 138.6007 };
                
                const initialZoom = tripPlannerInitialCoords ? 14 : 7;

                // If map already exists, just update its view
                if (map) {
                    map.setCenter(initialCenter);
                    map.setZoom(initialZoom);
                    if (tripPlannerInitialCoords) {
                        // Import AdvancedMarkerElement properly
                        const { AdvancedMarkerElement } = await google.maps.importLibrary("marker");
                        const marker = new AdvancedMarkerElement({
                            position: initialCenter,
                            map: map,
                            title: "Selected Location"
                        });
                        tripMarkers.push(marker);
                    }
                    tripPlannerInitialCoords = null;
                    return;
                }

                // Create new map
                geocoder = new google.maps.Geocoder();
                directionsRenderer = new google.maps.DirectionsRenderer();

                const mapOptions = {
                    center: initialCenter,
                    zoom: initialZoom,
                    disableDefaultUI: true,
                    zoomControl: true,
                    mapId: '9a6789cf3ff1289e'
                };
                map = new google.maps.Map(document.getElementById('map'), mapOptions);
                directionsRenderer.setMap(map);

                if (tripPlannerInitialCoords) {
                    // Import AdvancedMarkerElement properly
                    const { AdvancedMarkerElement } = await google.maps.importLibrary("marker");
                    const marker = new AdvancedMarkerElement({
                        position: initialCenter,
                        map: map,
                        title: "Selected Location"
                    });
                    tripMarkers.push(marker);
                }

                // Trigger resize immediately after creation
                google.maps.event.trigger(map, 'resize');
                map.setCenter(mapOptions.center);

                map.addListener('click', (e) => {
                    const latLng = e.latLng;
                    geocoder.geocode({ location: latLng }, (results, status) => {
                        if (status === 'OK') {
                            if (results[0]) {
                                document.getElementById('locationName').value = results[0].formatted_address;
                            } else {
                                alert('No results found for this location.');
                            }
                        } else {
                            alert('Geocoder failed due to: ' + status);
                        }
                    });
                });
            } catch (error) {
                console.error("Error initializing Google Map:", error);
                document.getElementById('map').innerHTML = '<p class="p-4 text-center text-red-600">Could not load map. Please check your internet connection and API key.</p>';
            } finally {
                tripPlannerInitialCoords = null; // Reset after use
            }
        };

        const addStopHandler = () => {
            if (stops.length >= 10) {
                alert("You can add a maximum of 10 stops.");
                return;
            }
            const locationName = document.getElementById('locationName').value.trim();
            const arrivalTime = document.getElementById('arrivalTime').value;
            const duration = document.getElementById('duration').value.trim();
            if (!locationName) {
                alert("Please enter an attraction name.");
                return;
            }
            stops.push({ locationName, arrivalTime, duration });
            renderStops();
            clearForm();
            analyzeBtn.disabled = true;
        };

        addStopBtn.addEventListener('click', addStopHandler);

        calculateTravelBtn.addEventListener('click', () => {
            const originalButtonText = "Calculate Travel";
            calculateTravelBtn.textContent = "Calculating...";
            calculateTravelBtn.disabled = true;

            if (stops.length < 2) {
                alert("Please add at least two stops to calculate travel times.");
                calculateTravelBtn.textContent = originalButtonText;
                calculateTravelBtn.disabled = false;
                return;
            }
            calculateAndDisplayRoute(stops, calculateTravelBtn, originalButtonText);
        });

        analyzeBtn.addEventListener('click', () => {
            analyzeItinerary(stops);
        });

        const getDirectionsBtn = document.getElementById('getDirectionsBtn');
        if (getDirectionsBtn) {
            getDirectionsBtn.addEventListener('click', () => {
                if (stops.length < 2) {
                    alert("Please add at least two stops to get directions.");
                    return;
                }
                const origin = encodeURIComponent(stops[0].locationName);
                const destination = encodeURIComponent(stops[stops.length - 1].locationName);
                const waypoints = stops.slice(1, -1).map(stop => encodeURIComponent(stop.locationName)).join('|');
                
                let googleMapsUrl = `https://www.google.com/maps/dir/?api=1&origin=${origin}&destination=${destination}`;
                if (waypoints) {
                    googleMapsUrl += `&waypoints=${waypoints}`;
                }
                
                window.open(googleMapsUrl, '_blank');
            });
        }

        function calculateAndDisplayRoute(currentStops, btn, btnText) {
            if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
                window.alert('Google Maps API failed to load. Please check your API key and project configuration.');
                btn.textContent = btnText;
                btn.disabled = false;
                return;
            }

            const directionsService = new google.maps.DirectionsService();
            const origin = currentStops[0].locationName;
            const destination = currentStops[currentStops.length - 1].locationName;
            const waypoints = currentStops.slice(1, -1).map(stop => ({
                location: stop.locationName,
                stopover: true
            }));

            directionsService.route({
                origin: origin,
                destination: destination,
                waypoints: waypoints,
                travelMode: google.maps.TravelMode.DRIVING
            }, (response, status) => {
                if (status === 'OK') {
                    let totalDurationSeconds = 0;
                    response.routes[0].legs.forEach((leg, index) => {
                        currentStops[index].travelToNext = leg.duration;
                        totalDurationSeconds += leg.duration.value;
                    });

                    renderStops();

                    const hours = Math.floor(totalDurationSeconds / 3600);
                    const minutes = Math.floor((totalDurationSeconds % 3600) / 60);
                    const totalTravelTime = `${hours} hours and ${minutes} minutes`;
                    // Instead of generating a link, render the route on the map
                    directionsRenderer.setDirections(response);

                    travelSummary.innerHTML = `
                        <h3 class="text-lg font-semibold">Travel Summary</h3>
                        <p class="text-slate-600 mt-2">Total estimated travel time: <strong>${totalTravelTime}</strong></p>
                    `;
                    
                    analyzeBtn.disabled = false;
                    if (getDirectionsBtn) getDirectionsBtn.disabled = false;
                } else {
                    window.alert('Directions request failed due to ' + status);
                }
                btn.textContent = btnText;
                btn.disabled = false;
            });
        }

        function shortenAddress(address) {
            if (!address) return '';
            // Remove Australia from the end, case-insensitive
            let shortened = address.replace(/, Australia$/i, '');
            // Remove state and postcode (e.g., ", SA 5007" or " SA 5007")
            shortened = shortened.replace(/,?\s[A-Z]{2,3}\s\d{4}$/, '');
            return shortened;
        }

        function analyzeItinerary(currentStops) {
            const resultsContainer = document.getElementById('analysis-results');
            resultsContainer.innerHTML = '';
            let analysisHTML = '<h3 class="text-lg font-semibold mb-2">Itinerary Analysis</h3><ul class="space-y-2">';
            let hasIssues = false;

            for (let i = 0; i < currentStops.length - 1; i++) {
                const stopA = currentStops[i];
                const stopB = currentStops[i + 1];

                if (stopA.travelToNext && stopA.arrivalTime && stopA.duration && stopB.arrivalTime) {
                    const travelTimeSeconds = stopA.travelToNext.value;
                    const arrivalA = new Date(`1970-01-01T${stopA.arrivalTime}`);
                    const durationHours = parseFloat(stopA.duration);
                    
                    if (isNaN(durationHours)) continue;

                    const departureA = new Date(arrivalA.getTime() + durationHours * 3600000);
                    const arrivalB = new Date(`1970-01-01T${stopB.arrivalTime}`);
                    const expectedArrivalB = new Date(departureA.getTime() + travelTimeSeconds * 1000);

                    if (expectedArrivalB > arrivalB) {
                        hasIssues = true;
                        const lateMinutes = Math.ceil((expectedArrivalB - arrivalB) / 60000);
                        const lateMessage = lateMinutes > 90
                            ? `You would arrive approximately <strong>${(lateMinutes / 60).toFixed(2)} hours</strong> beyond your stated arrival time.`
                            : `You would arrive approximately <strong>${lateMinutes} minute${lateMinutes !== 1 ? 's' : ''}</strong> beyond your stated arrival time.`;
                        const locationA = shortenAddress(stopA.locationName);
                        const locationB = shortenAddress(stopB.locationName);
                        analysisHTML += `<li class="p-3 bg-red-100 rounded-md"><span class="font-bold text-red-700">Conflict:</span> You might not have enough time to get from <strong>${locationA}</strong> to <strong>${locationB}</strong>. Estimated travel time is ${stopA.travelToNext.text}. ${lateMessage}</li>`;
                    } else {
                        const spareTimeMinutes = (arrivalB - expectedArrivalB) / 60000;
                        if (spareTimeMinutes > 15) {
                            const locationA = shortenAddress(stopA.locationName);
                            const locationB = shortenAddress(stopB.locationName);
                            const spareTimeMessage = spareTimeMinutes > 90
                                ? `about <strong>${(spareTimeMinutes / 60).toFixed(2)} hours</strong>`
                                : `about <strong>${Math.round(spareTimeMinutes)} minutes</strong>`;
                            analysisHTML += `<li class="p-3 bg-green-100 rounded-md"><span class="font-bold text-green-700">Suggestion:</span> You have ${spareTimeMessage} of spare time before you need to leave for <strong>${locationB}</strong>. You could extend your stay at <strong>${locationA}</strong>.</li>`;
                        }
                    }
                }
            }

            if (!hasIssues) {
                analysisHTML += `<li class="p-3 bg-sky-100 rounded-md"><span class="font-bold text-sky-700">All Good:</span> Your itinerary looks feasible!</li>`;
            }
            analysisHTML += '</ul>';
            resultsContainer.innerHTML = analysisHTML;
        }

        function renderStops() {
            stopsTableBody.innerHTML = '';
            stops.forEach((stop, index) => {
                const row = document.createElement('tr');
                const stopNumber = (index === 0) ? "Start" : (index === stops.length - 1 && stops.length > 1) ? "End" : index;
                row.innerHTML = `
                    <td>${stopNumber}</td>
                    <td>${stop.locationName}</td>
                    <td>${stop.arrivalTime}</td>
                    <td>${stop.duration}</td>
                    <td>${stop.travelToNext ? stop.travelToNext.text : 'N/A'}</td>
                    <td>
                        <button class="edit-stop bg-blue-500 text-white px-2 py-1 rounded" data-index="${index}">Edit</button>
                        <button class="remove-stop bg-red-500 text-white px-2 py-1 rounded" data-index="${index}">Remove</button>
                        <button class="move-stop-up bg-gray-500 text-white p-2 rounded disabled:opacity-50" data-index="${index}" ${index === 0 ? 'disabled' : ''}><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m18 15-6-6-6 6"/></svg></button>
                        <button class="move-stop-down bg-gray-500 text-white p-2 rounded disabled:opacity-50" data-index="${index}" ${index === stops.length - 1 ? 'disabled' : ''}><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg></button>
                    </td>
                `;
                stopsTableBody.appendChild(row);
            });

            const removeButtons = document.querySelectorAll('.remove-stop');
            removeButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    const buttonElement = e.target.closest('button');
                    const indexToRemove = parseInt(buttonElement.getAttribute('data-index'));
                    stops.splice(indexToRemove, 1);
                    resetTravelData();
                    renderStops();
                });
            });

            const moveUpButtons = document.querySelectorAll('.move-stop-up');
            moveUpButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    const buttonElement = e.target.closest('button');
                    const indexToMove = parseInt(buttonElement.getAttribute('data-index'));
                    moveStopUp(indexToMove);
                });
            });

            const moveDownButtons = document.querySelectorAll('.move-stop-down');
            moveDownButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    const buttonElement = e.target.closest('button');
                    const indexToMove = parseInt(buttonElement.getAttribute('data-index'));
                    moveStopDown(indexToMove);
                });
            });

            const editButtons = document.querySelectorAll('.edit-stop');
            editButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    const buttonElement = e.target.closest('button');
                    const indexToEdit = parseInt(buttonElement.getAttribute('data-index'));
                    const stop = stops[indexToEdit];
                    document.getElementById('locationName').value = stop.locationName;
                    document.getElementById('arrivalTime').value = stop.arrivalTime;
                    document.getElementById('duration').value = stop.duration;
                    
                    // Change Add button to Save button
                    const addStopBtn = document.getElementById('addStopBtn');
                    addStopBtn.textContent = 'Save Stop';
                    const saveStopHandler = () => {
                        stops[indexToEdit] = {
                            locationName: document.getElementById('locationName').value,
                            arrivalTime: document.getElementById('arrivalTime').value,
                            duration: document.getElementById('duration').value
                        };
                        resetTravelData();
                        renderStops();
                        clearForm();
                        // Reset button
                        addStopBtn.textContent = 'Add Stop';
                        addStopBtn.removeEventListener('click', saveStopHandler);
                        addStopBtn.addEventListener('click', addStopHandler);
                    };
                    addStopBtn.removeEventListener('click', addStopHandler);
                    addStopBtn.addEventListener('click', saveStopHandler);
                });
            });
        }

        function clearForm() {
            document.getElementById('locationName').value = '';
            document.getElementById('arrivalTime').value = '';
            document.getElementById('duration').value = '';
        }

        function resetTravelData() {
            stops.forEach(stop => delete stop.travelToNext);
            travelSummary.innerHTML = '';
            document.getElementById('analysis-results').innerHTML = '';
            analyzeBtn.disabled = true;
            if (directionsRenderer) {
                directionsRenderer.setDirections({routes: []});
            }
        }

        function moveStopUp(index) {
            if (index > 0) {
                const stopToMove = stops.splice(index, 1)[0];
                stops.splice(index - 1, 0, stopToMove);
                resetTravelData();
                renderStops();
            }
        }

        function moveStopDown(index) {
            if (index < stops.length - 1) {
                const stopToMove = stops.splice(index, 1)[0];
                stops.splice(index + 1, 0, stopToMove);
                resetTravelData();
                renderStops();
            }
        }
        const clearTripBtn = document.getElementById('clearTripBtn');
        if (clearTripBtn) {
            clearTripBtn.addEventListener('click', () => {
                // Clear the stops array
                stops = [];
                
                // Clear all markers from the map
                tripMarkers.forEach(marker => marker.setMap(null));
                tripMarkers = [];

                // Clear the route from the map
                if (directionsRenderer) {
                    directionsRenderer.setDirections({ routes: [] });
                }

                // Reset the UI
                renderStops(); // This will clear the table
                travelSummary.innerHTML = '';
                document.getElementById('analysis-results').innerHTML = '';
                analyzeBtn.disabled = true;
                if (getDirectionsBtn) getDirectionsBtn.disabled = true;
                clearForm();
            });
        }

        function getSavedItineraries() { return JSON.parse(localStorage.getItem('tripItineraries') || '[]'); }
        function saveTripItineraries(itineraries) { localStorage.setItem('tripItineraries', JSON.stringify(itineraries)); }

        function renderSavedItineraries() {
            savedItinerariesList.innerHTML = '';
            savedItineraries = getSavedItineraries();
            if (savedItineraries.length === 0) {
                savedItinerariesList.innerHTML = `<p class="text-center text-slate-500">No saved itineraries.</p>`;
                return;
            }
            savedItineraries.forEach((itinerary, index) => {
                const itineraryCard = document.createElement('li');
                itineraryCard.className = 'flex items-center justify-between p-2 bg-slate-100 rounded-md';
                itineraryCard.innerHTML = `
                    <span class="font-semibold">${escapeHTML(itinerary.name)}</span>
                    <div>
                        <button class="load-itinerary bg-sky-500 text-white px-3 py-1 rounded-md text-sm hover:bg-sky-600" data-index="${index}">Load</button>
                        <button class="delete-itinerary bg-red-500 text-white px-3 py-1 rounded-md text-sm hover:bg-red-600" data-index="${index}">Delete</button>
                    </div>
                `;
                savedItinerariesList.appendChild(itineraryCard);
            });
        }

        savedItinerariesList.addEventListener('click', (e) => {
            const target = e.target;
            if (target.classList.contains('load-itinerary')) {
                const index = parseInt(target.dataset.index);
                stops = [...savedItineraries[index].stops];
                renderStops();
                resetTravelData();
                alert(`Itinerary "${savedItineraries[index].name}" loaded.`);
            } else if (target.classList.contains('delete-itinerary')) {
                const index = parseInt(target.dataset.index);
                if (confirm(`Are you sure you want to delete the "${savedItineraries[index].name}" itinerary?`)) {
                    savedItineraries.splice(index, 1);
                    saveTripItineraries(savedItineraries);
                    renderSavedItineraries();
                }
            }
        });

        saveItineraryBtn.addEventListener('click', () => {
            if (stops.length < 2) {
                alert("Please add at least two stops to save an itinerary.");
                return;
            }
            const name = prompt("Enter a name for your itinerary:");
            if (name) {
                savedItineraries.push({ name, stops: [...stops] });
                saveTripItineraries(savedItineraries);
                renderSavedItineraries();
                alert(`Itinerary "${name}" saved!`);
            }
        });

        exportItineraryBtn.addEventListener('click', () => {
            if (stops.length === 0) {
                alert("Your itinerary is empty.");
                return;
            }

            let itineraryText = "My Itinerary:\n\n";
            stops.forEach((stop, index) => {
                itineraryText += `Stop ${index + 1}: ${stop.locationName}\n`;
                if (stop.arrivalTime) itineraryText += `  Arrival: ${stop.arrivalTime}\n`;
                if (stop.duration) itineraryText += `  Duration: ${stop.duration} hours\n`;
                if (stop.travelToNext) itineraryText += `  Travel to next: ${stop.travelToNext.text}\n`;
                itineraryText += "\n";
            });

            if (navigator.share) {
                navigator.share({
                    title: 'My Travel Itinerary',
                    text: itineraryText,
                }).catch(console.error);
            } else {
                navigator.clipboard.writeText(itineraryText).then(() => {
                    alert("Itinerary copied to clipboard!");
                });
            }
        });

        renderSavedItineraries();
    }
    
    // --- NOTES SCREEN LOGIC ---
    const packingList = document.getElementById('packing-list');
    const packingListInput = document.getElementById('packing-list-input');
    const addPackingItemButton = document.getElementById('add-packing-item-button');
    const interestsTextarea = document.getElementById('interests-textarea');
    const documentFolderInput = document.getElementById('document-folder-input');
    const selectDocumentFolderButton = document.getElementById('select-document-folder-button');
    const documentListNotes = document.getElementById('document-list-notes');
    
    let packingItems = JSON.parse(localStorage.getItem('auraPackingList')) || [];
    
    function renderPackingList() {
        packingList.innerHTML = '';
        if (packingItems.length === 0) {
            packingList.innerHTML = '<p class="text-sm text-slate-500">Your packing list is empty.</p>';
            return;
        }
        packingItems.forEach((item, index) => {
            const itemEl = document.createElement('div');
            itemEl.className = 'flex items-center justify-between p-2 bg-slate-100 rounded-md';
            itemEl.innerHTML = `
                <div class="flex items-center">
                    <input type="checkbox" id="item-${index}" ${item.checked ? 'checked' : ''} class="h-4 w-4 text-sky-600 border-gray-300 rounded focus:ring-sky-500">
                    <label for="item-${index}" class="ml-3 text-slate-700 ${item.checked ? 'line-through' : ''}">${escapeHTML(item.text)}</label>
                </div>
                <button data-index="${index}" class="delete-packing-item text-slate-400 hover:text-red-500">&times;</button>
            `;
            packingList.appendChild(itemEl);
        });
    }
    
    function savePackingList() {
        localStorage.setItem('auraPackingList', JSON.stringify(packingItems));
    }
    
    addPackingItemButton.addEventListener('click', () => {
        const text = packingListInput.value.trim();
        if (text) {
            packingItems.push({ text, checked: false });
            packingListInput.value = '';
            savePackingList();
            renderPackingList();
        }
    });
    
    packingList.addEventListener('click', (e) => {
        if (e.target.type === 'checkbox') {
            const index = parseInt(e.target.id.split('-')[1]);
            packingItems[index].checked = e.target.checked;
            savePackingList();
            renderPackingList();
        }
        if (e.target.classList.contains('delete-packing-item')) {
            const index = parseInt(e.target.dataset.index);
            packingItems.splice(index, 1);
            savePackingList();
            renderPackingList();
        }
    });
    
    interestsTextarea.addEventListener('input', () => {
        localStorage.setItem('auraInterests', interestsTextarea.value);
    });
    
    selectDocumentFolderButton.addEventListener('click', () => {
        documentFolderInput.click();
    });
    
    documentFolderInput.addEventListener('change', (e) => {
        documentListNotes.innerHTML = '';
        if (e.target.files.length > 0) {
            const files = Array.from(e.target.files);
            files.forEach(file => {
                const fileEl = document.createElement('button');
                fileEl.className = 'w-full text-left p-2 bg-slate-100 rounded-md text-sm text-slate-700 hover:bg-slate-200';
                fileEl.textContent = file.name;
                fileEl.onclick = () => {
                    const singleFileInput = document.createElement('input');
                    singleFileInput.type = 'file';
                    singleFileInput.style.display = 'none';
                    // We can't pre-select the file, but we can open the file dialog.
                    // This is a browser security limitation.
                    singleFileInput.click();
                };
                documentListNotes.appendChild(fileEl);
            });
        }
    });
    
    // Load initial notes data
    interestsTextarea.value = localStorage.getItem('auraInterests') || '';
    renderPackingList();
    
});

// Event listeners - restored to original simple form
if (closeSettingsButton) closeSettingsButton.addEventListener('click', () => settingsModal.classList.add('hidden'));
if (settingsForm) settingsForm.addEventListener('submit', handleSaveSettings);
if (homeSosButton) homeSosButton.addEventListener('click', handleSos);
if (cancelSos) cancelSos.addEventListener('click', () => sosOptionsModal.classList.add('hidden'));
if (alertContactButton) alertContactButton.addEventListener('click', () => handleSmsAlert(false));
if (shareLocationButtonHome) shareLocationButtonHome.addEventListener('click', handleHomeShareClick);
if (closeActionConfirmModal) closeActionConfirmModal.addEventListener('click', () => actionConfirmModal.classList.add('hidden'));

// Debug panel removed - buttons are working properly

let liveJourneyId = null;
let liveWatchId = null;

function handleHomeShareClick() {
    if (liveJourneyId) {
        stopLiveJourney();
    } else {
        startLiveJourney();
    }
}

async function startLiveJourney() {
    const statusElement = document.getElementById('sound-scape-status');

    if (liveJourneyId) {
        if (confirm("A live journey is already active. Do you want to stop it?")) {
            stopLiveJourney();
        }
        return;
    }

    // Show location privacy consent dialog
    const consentGiven = await showLocationConsentDialog();
    if (!consentGiven) {
        setSoundScapeStatus('Location sharing cancelled.');
        setTimeout(() => { setSoundScapeStatus(''); }, 3000);
        return;
    }

    setSoundScapeStatus('Getting your location...');
    shareLocationButtonHome.innerHTML = 'Getting<br>Location...';

    try {
        await authReadyPromise;

        // Get current position first
        const position = await new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, { enableHighAccuracy: true });
        });

        const { latitude, longitude } = position.coords;
        
        // Create a new live journey document
        const journeyId = Date.now().toString();
        const journeyDocRef = window.fb.doc(db, 'journeys', journeyId);

        await window.fb.setDoc(journeyDocRef, {
            creatorId: userId,
            latitude: latitude,
            longitude: longitude,
            active: true,
            createdAt: window.fb.serverTimestamp(),
            lastUpdated: window.fb.serverTimestamp()
        });

        // Update rate limiting tracking after successful journey creation
        try {
            const userJourneyRateLimitRef = window.fb.doc(db, `users/${userId}/rateLimiting/lastJourneyTime`);
            await window.fb.setDoc(userJourneyRateLimitRef, { timestamp: window.fb.serverTimestamp() });
        } catch (rateLimitError) {
            // Log but don't fail the journey creation
            logSecureError('Rate limit tracking failed', rateLimitError);
        }

        liveJourneyId = journeyId;
        
        // Start watching position
        liveWatchId = navigator.geolocation.watchPosition(
            async (position) => {
                if (liveJourneyId) {
                    const { latitude, longitude } = position.coords;
                    await window.fb.updateDoc(journeyDocRef, {
                        latitude: latitude,
                        longitude: longitude,
                        lastUpdated: window.fb.serverTimestamp()
                    });
                }
            },
            (error) => {
                console.error("Error watching position:", error);
            },
            { enableHighAccuracy: true, maximumAge: 10000, timeout: 15000 }
        );

        // Create tracking link
        const trackingUrl = `${window.location.origin}/journey.html?id=${journeyId}`;
        const shareData = {
            title: 'Follow My Live Location',
            text: `Follow my live location here: ${trackingUrl} Live`,
            url: trackingUrl
        };

        setSoundScapeStatus('Preparing to share live location...');

        if (navigator.share) {
            await navigator.share(shareData);
            setSoundScapeStatus('Live location shared!');
        } else {
            navigator.clipboard.writeText(trackingUrl);
            alert('Live tracking link copied to clipboard. Share it with your contact.');
            setSoundScapeStatus('Live tracking link copied!');
        }

        // Update button to show stop option
        shareLocationButtonHome.innerHTML = 'Stop<br>Sharing';
        setSoundScapeStatus('Live location sharing active');

    } catch (error) {
        // Security: Log detailed error for developers, show generic message to users
        logSecureError('Live journey start failed', error);
        let errorMessage = 'Could not start live sharing. ';
        if (error.code === 1) { // PERMISSION_DENIED
            errorMessage = 'Location permission denied. Please enable it in your browser settings.';
        } else {
            errorMessage = 'Unable to start location sharing. Please check your connection and try again.';
        }
        setSoundScapeStatus(errorMessage);
        shareLocationButtonHome.innerHTML = 'Share My<br>Location';
        setTimeout(() => { setSoundScapeStatus(''); }, 3000);
    }
}

async function stopLiveJourney() {
    if (liveWatchId) {
        navigator.geolocation.clearWatch(liveWatchId);
        liveWatchId = null;
    }
    if (liveJourneyId && db) {
        // Use the same path structure as in startLiveJourney
        const journeyDocRef = window.fb.doc(db, 'journeys', liveJourneyId);
        try {
            await window.fb.updateDoc(journeyDocRef, { active: false });
            console.log('Journey stopped successfully');
        } catch (error) {
            console.error('Error stopping journey:', error);
            // Don't throw error, just log it since the main goal is to stop local tracking
        }
        liveJourneyId = null;
    }
    shareLocationButtonHome.innerHTML = 'Share My<br>Location';
    setSoundScapeStatus('Live sharing stopped.');
    setTimeout(() => { setSoundScapeStatus(''); }, 3000);
}

stopJourneyButton.addEventListener('click', stopJourney);
copyJourneyLinkButton.addEventListener('click', copyJourneyLink);

messageForm.addEventListener('submit', handleMessageSubmit);
accessForm.addEventListener('submit', handleAccessCode);

fileInput.addEventListener('change', handleFileUpload);

// Listener for the new dedicated photo upload button
const photoUploadButton = document.getElementById('photo-upload-button');
if (photoUploadButton) {
    photoUploadButton.addEventListener('click', () => fileInput.click());
}

soundScapeButton.addEventListener('click', handleSoundScape);
navHome.addEventListener('click', () => showScreen('home'));
navSolo.addEventListener('click', () => {
    showScreen('solo');
    // The initMap callback will handle the map initialization,
    // but we call it here as well in case the user navigates
    // to the tab after the initial script load has already completed.
    initTripPlannerMap();
});
navGroup.addEventListener('click', () => {
    showScreen('group');
    // Default to South Australia and fetch directory
    regionSelect.value = 'South Australia';
    fetchAndRenderDirectory('South Australia');
    directoryList.classList.remove('hidden');
});
navInfo.addEventListener('click', () => showScreen('info'));
let isCodexScriptLoaded = false;

function loadCodexScript(callback) {
    if (isCodexScriptLoaded) {
        if (callback) callback();
        return;
    }

    const script = document.createElement('script');
    script.src = './codex.js';
    script.onload = () => {
        console.log('✅ codex.js loaded dynamically.');
        isCodexScriptLoaded = true;
        if (callback) callback();
    };
    script.onerror = () => {
        console.error('❌ Failed to load codex.js.');
        const codexRoot = document.getElementById('codex-root');
        if (codexRoot) {
            codexRoot.innerHTML = '<div class="p-8 text-center"><h2 class="text-xl font-bold text-red-600">Codex Loading Failed</h2><p>Could not load the required script.</p></div>';
        }
    };
    document.body.appendChild(script);
}

navCodex.addEventListener('click', () => {
    showScreen('codex');
    loadCodexScript(() => {
        // This code runs after codex.js is loaded
        if (window.CodexApp && typeof window.CodexApp.render === 'function') {
            const codexRoot = document.getElementById('codex-root');
            window.CodexApp.render(codexRoot);
        } else {
            console.error('CodexApp.render is not a function. Check the build process.');
        }

        const urlParams = new URLSearchParams(window.location.search);
        const existingJourney = urlParams.get('journey') || urlParams.get('codex');

        if (existingJourney) {
            window.dispatchEvent(new CustomEvent('journey-load', { detail: { journeyId: existingJourney } }));
        }
    });
});

document.getElementById('info-sos-settings-button').addEventListener('click', () => settingsModal.classList.remove('hidden'));
document.getElementById('info-user-guide-button').addEventListener('click', () => showScreen('user-guide'));
document.getElementById('info-sponsors-button').addEventListener('click', () => showScreen('sponsors'));
document.getElementById('info-notes-button').addEventListener('click', () => showScreen('notes'));

document.getElementById('guide-back-to-info').addEventListener('click', () => showScreen('info'));
document.getElementById('sponsors-back-to-info').addEventListener('click', () => showScreen('info'));
document.getElementById('notes-back-to-info').addEventListener('click', () => showScreen('info'));


chatModeSwitcher.addEventListener('click', (e) => {
    const newMode = e.target.id;
    if (newMode === 'mode-photos') {
        currentViewMode = 'photos';
    } else {
        currentViewMode = 'chat';
    }
    updateUIForChatView();
    renderMessages(localMessages);
});

regionSelect.addEventListener('change', () => {
    const selectedRegion = regionSelect.value;
    if (selectedRegion) {
        fetchAndRenderDirectory(selectedRegion);
        directoryList.classList.remove('hidden');
    } else {
        directoryList.classList.add('hidden');
    }
});

async function fetchAndRenderDirectory(region) {
    directoryList.innerHTML = '<p class="text-center text-slate-500">Loading...</p>';

    try {
        await authReadyPromise;
        if (!db) {
            throw new Error('Database not initialized');
        }
        const directoryPath = `artifacts/${appId}/public/data/directory`;
        const directoryCollection = window.fb.collection(db, directoryPath);
        const q = window.fb.query(directoryCollection, window.fb.where("regions", "array-contains", region));

        const querySnapshot = await window.fb.getDocs(q);
        
        directoryList.innerHTML = '';
        if (querySnapshot.empty) {
            directoryList.innerHTML = '<p class="text-center text-slate-500">No activities found for this region.</p>';
            return;
        }
        
        querySnapshot.forEach((doc) => {
            const item = doc.data();
            const itemCard = document.createElement('div');
            itemCard.className = 'p-4 bg-white border rounded-lg shadow-sm cursor-pointer hover:bg-slate-50';
            // Join the regions array for display
            const regionText = item.regions ? item.regions.join(', ') : '';
            itemCard.innerHTML = `
                <div class="flex items-center gap-4">
                    <img src="${item.featured_image || 'https://placehold.co/64x64/a3e635/ffffff?text=A'}" class="w-16 h-16 rounded-md object-cover" onerror="this.onerror=null;this.src='https://placehold.co/64x64/a3e635/ffffff?text=A';">
                    <div>
                        <p class="font-bold text-slate-800">${item.name}</p>
                        <p class="text-sm text-slate-600">${regionText}</p>
                    </div>
                </div>
            `;
            itemCard.onclick = () => showDirectoryItemDetails(item);
            directoryList.appendChild(itemCard);
        });
    } catch (error) {
        // Security: Log detailed error for developers, show generic message to users
        logSecureError('Directory loading failed', error);
        directoryList.innerHTML = '<p class="text-center text-red-500">Unable to load directory. Please check your connection and try again.</p>';
    }
}

function showDirectoryItemDetails(item) {
    directoryItemContent.innerHTML = `
        <img src="${item.featured_image || 'https://placehold.co/600x400/a3e635/ffffff?text=A'}" class="w-full h-48 object-contain rounded-t-lg" onerror="this.onerror=null;this.src='https://placehold.co/600x400/a3e635/ffffff?text=A';">
        <div class="p-6 text-left">
            <h2 class="text-2xl font-bold text-slate-800">${item.name}</h2>
            <p class="text-slate-400 mt-2 mb-4">${item.regions ? item.regions.join(', ') : ''}</p>
            <div class="text-slate-700 text-lg leading-relaxed">${item.description}</div>
        </div>
    `;
    directoryItemModal.classList.remove('hidden');

    directoryItemContent.addEventListener('click', (e) => {
        if (e.target.tagName === 'A' && e.target.href) {
            e.preventDefault();
            openInAppBrowser(e.target.href);
        }
    });
}

closeDirectoryModal.addEventListener('click', () => {
    directoryItemModal.classList.add('hidden');
});

// Enhanced Input Validation Functions
function validateMessageInput(message) {
    if (!message || typeof message !== 'string') {
        return { isValid: false, error: 'Message cannot be empty' };
    }

    // Check length
    if (message.length > 2000) {
        return { isValid: false, error: 'Message too long (maximum 2000 characters)' };
    }

    // Check for suspicious patterns
    const suspiciousPatterns = [
        /<script/i,
        /javascript:/i,
        /on\w+\s*=/i,
        /<iframe/i,
        /<object/i,
        /<embed/i,
        /data:text\/html/i,
        /vbscript:/i
    ];

    for (const pattern of suspiciousPatterns) {
        if (pattern.test(message)) {
            return { isValid: false, error: 'Message contains potentially unsafe content' };
        }
    }

    return { isValid: true };
}

function sanitizeMessageInput(message) {
    if (!message || typeof message !== 'string') return '';

    // Use XSS protection if available
    if (window.XSSProtection) {
        return window.XSSProtection.escapeHTML(message);
    }

    // Fallback sanitization
    return message
        .replace(/[<>]/g, '') // Remove angle brackets
        .replace(/javascript:/gi, '') // Remove javascript: protocol
        .replace(/on\w+=/gi, '') // Remove event handlers
        .trim()
        .substring(0, 2000); // Limit length
}

function validatePhoneNumber(phone) {
    if (!phone || typeof phone !== 'string') return false;

    // Remove all non-digit characters except + at the beginning
    const cleaned = phone.replace(/[^\d+]/g, '');

    // Check if it's a valid phone number format
    const phoneRegex = /^(\+?1?)?[0-9]{10,15}$/;
    return phoneRegex.test(cleaned);
}

function sanitizeInput(input) {
    if (!input || typeof input !== 'string') return '';

    return input
        .replace(/[<>]/g, '') // Remove angle brackets
        .replace(/javascript:/gi, '') // Remove javascript: protocol
        .replace(/on\w+=/gi, '') // Remove event handlers
        .replace(/['"]/g, '') // Remove quotes
        .trim()
        .substring(0, 500); // Limit length
}

// Secure Error Handling Functions
function logSecureError(context, error, additionalInfo = {}) {
    // Create sanitized error info for logging
    const errorInfo = {
        context: context,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent.substring(0, 100),
        url: window.location.href,
        ...additionalInfo
    };

    // Log detailed error information
    console.error(`[${context}] Detailed error:`, {
        ...errorInfo,
        error: {
            name: error.name,
            message: error.message,
            stack: error.stack,
            code: error.code
        }
    });

    // Store error for potential reporting (without sensitive data)
    try {
        const errorLog = JSON.parse(localStorage.getItem('appErrorLog') || '[]');
        errorLog.push({
            context: errorInfo.context,
            timestamp: errorInfo.timestamp,
            errorType: error.name || 'Unknown',
            userAgent: errorInfo.userAgent
        });

        // Keep only last 50 errors
        if (errorLog.length > 50) {
            errorLog.splice(0, errorLog.length - 50);
        }

        localStorage.setItem('appErrorLog', JSON.stringify(errorLog));
    } catch (storageError) {
        console.warn('Could not store error log:', storageError);
    }
}

function showUserError(message, duration = 5000) {
    // Create or update user error display
    let errorDisplay = document.getElementById('user-error-display');
    if (!errorDisplay) {
        errorDisplay = document.createElement('div');
        errorDisplay.id = 'user-error-display';
        errorDisplay.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 max-w-sm text-center';
        document.body.appendChild(errorDisplay);
    }

    errorDisplay.textContent = message;
    errorDisplay.style.display = 'block';

    // Auto-hide after duration
    setTimeout(() => {
        if (errorDisplay) {
            errorDisplay.style.display = 'none';
        }
    }, duration);
}

function getErrorSummary() {
    // Function to get error summary for debugging

    try {
        const errorLog = JSON.parse(localStorage.getItem('appErrorLog') || '[]');
        return {
            totalErrors: errorLog.length,
            recentErrors: errorLog.slice(-10),
            errorTypes: [...new Set(errorLog.map(e => e.errorType))]
        };
    } catch (error) {
        return 'Could not retrieve error summary';
    }
}
