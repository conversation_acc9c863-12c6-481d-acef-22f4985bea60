// Firebase Native Integration for Capacitor
// This file handles Firebase initialization for both native and web platforms

import { Capacitor } from '@capacitor/core';

let firebaseApp = null;
let auth = null;
let db = null;
let isNativePlatform = false;

// Enhanced platform detection
function detectPlatform() {
    // Check multiple indicators for native platform
    const capacitorNative = Capacitor.isNativePlatform();
    const hasCapacitorPlugins = window.Capacitor && window.Capacitor.Plugins;
    const userAgent = navigator.userAgent;
    const isAndroidWebView = userAgent.includes('wv') && userAgent.includes('Android');
    const isCapacitorApp = window.location.protocol === 'capacitor:';
    const isIOS = Capacitor.getPlatform() === 'ios';

    // Use native Firebase for both iOS and Android
    isNativePlatform = capacitorNative || isAndroidWebView || isCapacitorApp;

    console.log('🔥 Firebase Native: Platform detection details:', {
        capacitorNative,
        hasCapacitorPlugins,
        isAndroidWebView,
        isCapacitorApp,
        isIOS,
        userAgent: userAgent.substring(0, 100),
        protocol: window.location.protocol,
        finalDecision: isNativePlatform ? 'Native' : 'Web'
    });

    return isNativePlatform;
}

async function initializeNativeFirebase() {
    console.log('🔥 Firebase Native: Starting native Firebase initialization...');

    try {
        // Import native Firebase plugins
        console.log('🔥 Firebase Native: Importing native plugins...');
        const { FirebaseApp } = await import('@capacitor-firebase/app');
        const { FirebaseAuthentication } = await import('@capacitor-firebase/authentication');
        const { FirebaseFirestore } = await import('@capacitor-firebase/firestore');

        console.log('✅ Firebase Native: Native plugins imported successfully');

        // Initialize Firebase App with native plugin - uses GoogleService-Info.plist for iOS
        console.log('🔥 Firebase Native: Initializing Firebase app...');
        await FirebaseApp.initializeApp();
        console.log('✅ Firebase Native: Native app initialized with platform config file');

        // Test if Firebase is actually working
        console.log('🔥 Firebase Native: Testing Firebase connection...');
        const appInfo = await FirebaseApp.getApp();
        console.log('✅ Firebase Native: App info retrieved:', appInfo);

    } catch (initError) {
        console.error('❌ Firebase Native: Initialization failed:', initError);
        throw initError;
    }

    // Test native authentication
    try {
        const currentUser = await FirebaseAuthentication.getCurrentUser();
        console.log('✅ Firebase Native: Auth plugin working, current user:', currentUser.user ? 'exists' : 'none');
    } catch (authTestError) {
        console.log('⚠️ Firebase Native: Auth test failed, but continuing:', authTestError.message);
    }
    
    // Create auth wrapper that matches web SDK interface
    auth = {
        currentUser: null,
        onAuthStateChanged: async (callback) => {
            // First, sign in anonymously to avoid "No user signed in" errors
            try {
                console.log('🔥 Firebase Native: Attempting anonymous sign-in to prevent auth errors...');
                const result = await FirebaseAuthentication.signInAnonymously();
                auth.currentUser = { uid: result.user.uid };
                console.log('✅ Firebase Native: Anonymous sign-in successful');
                callback(auth.currentUser);
            } catch (error) {
                console.error('❌ Firebase Native: Anonymous sign-in failed:', error);
                auth.currentUser = null;
                callback(null);
            }
            
            // Listen for auth state changes
            FirebaseAuthentication.addListener('authStateChange', (change) => {
                console.log('🔥 Firebase Native: Auth state changed:', change.user ? 'User signed in' : 'User signed out');
                if (change.user) {
                    auth.currentUser = { uid: change.user.uid };
                    callback(auth.currentUser);
                } else {
                    auth.currentUser = null;
                    callback(null);
                }
            });
        },
        signInAnonymously: async () => {
            try {
                const result = await FirebaseAuthentication.signInAnonymously();
                auth.currentUser = { uid: result.user.uid };
                console.log('✅ Firebase Native: Anonymous sign-in successful');
                return { user: auth.currentUser };
            } catch (error) {
                console.error('❌ Firebase Native: Anonymous sign-in failed:', error);
                throw error;
            }
        },
        signInWithCustomToken: async (token) => {
            try {
                const result = await FirebaseAuthentication.signInWithCustomToken({ token });
                auth.currentUser = { uid: result.user.uid };
                return { user: auth.currentUser };
            } catch (error) {
                console.error('❌ Firebase Native: Custom token sign-in failed:', error);
                throw error;
            }
        }
    };
    
    // Create Firestore wrapper that matches web SDK interface
    db = {
        collection: (path) => ({
            doc: (id) => ({
                get: async () => {
                    try {
                        const result = await FirebaseFirestore.getDocument({
                            reference: `${path}/${id}`
                        });
                        return {
                            exists: () => result.snapshot.exists,
                            data: () => result.snapshot.data
                        };
                    } catch (error) {
                        console.error('Native Firestore get failed:', error);
                        throw error;
                    }
                },
                set: async (data) => {
                    try {
                        await FirebaseFirestore.setDocument({
                            reference: `${path}/${id}`,
                            data: data
                        });
                        console.log('✅ Firebase Native: Document set successfully');
                    } catch (error) {
                        console.error('❌ Firebase Native: Document set failed:', error);
                        throw error;
                    }
                },
                update: async (data) => {
                    try {
                        await FirebaseFirestore.updateDocument({
                            reference: `${path}/${id}`,
                            data: data
                        });
                        console.log('✅ Firebase Native: Document updated successfully');
                    } catch (error) {
                        console.error('❌ Firebase Native: Document update failed:', error);
                        throw error;
                    }
                }
            }),
            add: async (data) => {
                try {
                    const result = await FirebaseFirestore.addDocument({
                        reference: path,
                        data: data
                    });
                    console.log('✅ Firebase Native: Document added successfully');
                    return { id: result.reference.split('/').pop() };
                } catch (error) {
                    console.error('❌ Firebase Native: Document add failed:', error);
                    throw error;
                }
            },
            onSnapshot: (callback) => {
                // Note: Real-time listeners are more complex in native
                console.warn('Native Firestore: onSnapshot not fully implemented, using polling');
                return () => {}; // Return unsubscribe function
            }
        }),
        doc: (path) => ({
            get: async () => {
                try {
                    const result = await FirebaseFirestore.getDocument({
                        reference: path
                    });
                    return {
                        exists: () => result.snapshot.exists,
                        data: () => result.snapshot.data
                    };
                } catch (error) {
                    console.error('Native Firestore doc get failed:', error);
                    throw error;
                }
            },
            set: async (data) => {
                try {
                    await FirebaseFirestore.setDocument({
                        reference: path,
                        data: data
                    });
                    console.log('✅ Firebase Native: Document set successfully');
                } catch (error) {
                    console.error('❌ Firebase Native: Document set failed:', error);
                    throw error;
                }
            },
            update: async (data) => {
                try {
                    await FirebaseFirestore.updateDocument({
                        reference: path,
                        data: data
                    });
                    console.log('✅ Firebase Native: Document updated successfully');
                } catch (error) {
                    console.error('❌ Firebase Native: Document update failed:', error);
                    throw error;
                }
            }
        })
    };
    
    // Ensure we return npm-based Firebase instances for compatibility with app.js firestore functions
    try {
        const { auth: webAuth, db: webDb } = await import('../firebase-init.js');
        auth = webAuth;
        db = webDb;
        console.log('✅ Firebase Native: Using npm-based Firebase instances on native for compatibility');
    } catch (e) {
        console.warn('⚠️ Firebase Native: Failed to load firebase-init.js on native. Firestore calls may fail.', e);
    }

    console.log('✅ Firebase Native: All native services initialized successfully');
    return { auth, db, isNativePlatform: true };
}

async function initializeWebFirebase() {
    console.log('🔥 Firebase Native: Initializing web SDK (npm modules)...');

    // Reuse the existing npm-based initialization to avoid mixing SDK sources
    // This ensures the Firestore instance type matches the functions imported in app.js
    const { auth: webAuth, db: webDb } = await import('../firebase-init.js');

    auth = webAuth;
    db = webDb;

    console.log('✅ Firebase Native: Web SDK initialized via firebase-init.js');
    return { auth, db, isNativePlatform: false };
}

async function initializeFirebaseNative() {
    try {
        const platformIsNative = detectPlatform();
        
        if (platformIsNative) {
            try {
                return await initializeNativeFirebase();
            } catch (nativeError) {
                console.error('❌ Firebase Native: Native initialization failed, falling back to web SDK:', nativeError);
                // Fall back to web SDK if native fails
                return await initializeWebFirebase();
            }
        } else {
            return await initializeWebFirebase();
        }
        
    } catch (error) {
        console.error('❌ Firebase Native: Complete initialization failed:', error);
        throw error;
    }
}

// Export the initialization function and platform detection
export { initializeFirebaseNative };

// Export platform detection function that can be called to get current state
export function getIsNativePlatform() {
    return isNativePlatform;
}

// Export platform detection function for immediate use
export function detectNativePlatform() {
    return detectPlatform();
}
