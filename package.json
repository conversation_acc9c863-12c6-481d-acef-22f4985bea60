{"name": "monoloci-activity-companion", "version": "1.0.0", "description": "Monoloci: Your Activity Companion - A safety-focused mobile app for activity participants", "main": "index.html", "scripts": {"dev": "python start-server.py", "build": "node build.js", "deploy": "npm run build && echo 'Build complete! Deploy the dist/ folder to your hosting service'", "serve": "python -m http.server 8000", "serve:https": "http-server -p 8443 -S -C cert.pem -K key.pem", "test": "echo 'Open firebase-test.html to test Firebase connectivity'", "security:audit": "npm audit", "security:fix": "npm audit fix", "security:test": "node security-test-suite.js", "security:check": "npm run security:audit && npm run security:test", "cap:build": "npm run build && npx cap sync", "cap:sync": "npx cap sync", "cap:copy:ios": "npx cap copy ios", "cap:open:android": "npx cap open android", "cap:run:android": "npx cap run android", "cap:open:ios": "npx cap open ios", "cap:run:ios": "npx cap run ios", "android:dev": "npm run cap:build && npm run cap:open:android", "ios:build": "npm run build && npx cap sync ios", "ios:init": "npx cap add ios || true && (cd ios/App && pod install || true)", "ios:dev": "npm run ios:build && npm run cap:open:ios"}, "keywords": ["activity", "safety", "mobile", "pwa", "firebase", "emergency"], "author": "Monoloci Development Team", "license": "GPL-2.0", "dependencies": {"@capacitor-community/admob": "^7.0.3", "@capacitor-firebase/app": "^7.3.0", "@capacitor-firebase/authentication": "^7.3.0", "@capacitor-firebase/firestore": "^7.3.0", "@capacitor/android": "^7.4.2", "@capacitor/browser": "^7.0.1", "@capacitor/camera": "^7.0.1", "@capacitor/cli": "^7.4.2", "@capacitor/clipboard": "^7.0.1", "@capacitor/core": "^7.4.2", "@capacitor/device": "^7.0.1", "@capacitor/filesystem": "^7.1.3", "@capacitor/geolocation": "^7.1.4", "@capacitor/ios": "^7.4.2", "@capacitor/push-notifications": "^7.0.1", "@capacitor/share": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@capawesome/capacitor-file-picker": "^7.2.0", "capacitor-voice-recorder": "^7.0.6", "firebase": "^12.0.0", "framer-motion": "^11.2.12", "react": "^19.1.0", "react-dom": "^19.1.0", "tone": "^15.0.4"}, "devDependencies": {"@capacitor/assets": "^3.0.5", "@eslint/js": "^9.31.0", "@tailwindcss/postcss": "^4.1.11", "autoprefixer": "^10.4.19", "clean-css": "^5.3.2", "cssnano": "^7.0.1", "esbuild": "^0.25.6", "eslint": "^9.31.0", "eslint-plugin-security": "^3.0.1", "firebase-tools": "^14.11.1", "http-server": "^14.1.1", "https-localhost": "^4.7.1", "postcss-cli": "^11.0.0", "postcss-import": "^16.1.0", "tailwindcss": "^4.1.11", "terser": "^5.19.4", "typescript": "^5.8.3"}, "repository": {"type": "git", "url": "https://github.com/yourusername/Monoloci-app.git"}, "homepage": "https://Monoloci-app-backend.web.app/", "engines": {"node": ">=14.0.0"}}